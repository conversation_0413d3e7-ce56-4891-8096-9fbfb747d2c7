<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta
      name="viewport"
      content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width<% if (ctx.mode.cordova || ctx.mode.capacitor) { %>, viewport-fit=cover<% } %>"
    />
    <meta name="facebook-domain-verification" content="cn6obudpbjckslu7ib0yxmtfy0k8ji" />
    <!-- Primary Meta Tags -->
    <title><%= process.env.APP_TITLE %></title>
    <meta name="title" content="<%= process.env.APP_TITLE %>" />
    <meta name="description" content="<%= process.env.APP_DESCRIPTION %>" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="<%= process.env.APP_END_POINT %>" />
    <meta property="og:title" content="<%= process.env.APP_TITLE %>" />
    <meta property="og:description" content="<%= process.env.APP_DESCRIPTION %>" />
    <meta property="og:image" content="<%= process.env.APP_END_POINT %>/sentosa-metaimage.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="<%= process.env.APP_END_POINT %>" />
    <meta property="twitter:title" content="<%= process.env.APP_TITLE %>" />
    <meta property="twitter:description" content="<%= process.env.APP_DESCRIPTION %>" />
    <meta
      property="twitter:image"
      content="<%= process.env.APP_END_POINT %>/sentosa-metaimage.png"
    />

    <link rel="apple-touch-icon" sizes="57x57" href="icons/apple-icon-57x57.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="icons/apple-icon-60x60.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="icons/apple-icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="icons/apple-icon-76x76.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="icons/apple-icon-114x114.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="icons/apple-icon-120x120.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="icons/apple-icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="icons/apple-icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="icons/apple-icon-180x180.png" />
    <link rel="icon" type="image/png" sizes="192x192" href="icons/android-icon-192x192.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="icons/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="icons/favicon-96x96.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="icons/favicon-16x16.png" />

    <meta name="msapplication-TileColor" content="#ffffff" />
    <meta name="msapplication-TileImage" content="icons/ms-icon-144x144.png" />
    <meta name="theme-color" content="#ffffff" />

    <script>
      window.mobileCheck = function () {
        let check = false;
        (function (a) {
          if (
            /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(
              a,
            ) ||
            /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(
              a.substring(0, 4),
            )
          )
            check = true;
        })(navigator.userAgent || navigator.vendor || window.opera);
        return check;
      };
    </script>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-ZD6Z4Y7ERT"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());

      gtag('config', 'G-ZD6Z4Y7ERT');
    </script>

    <!-- Meta Pixel Code -->
    <script>
      !(function (f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function () {
          n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = '2.0';
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '870188824858773');
      fbq('track', 'PageView');
    </script>

    <!-- End Meta Pixel Code -->
    <script>
      !(function (f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function () {
          n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = '2.0';
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '1116855446286098');
      fbq('track', 'PageView');
    </script>

    <meta name="“facebook-domain-verification”" content="“cn6obudpbjckslu7ib0yxmtfy0k8ji”" />
  </head>
  <body>
    <noscript>
      <img
        height="1"
        width="1"
        style="display: none"
        src="https://www.facebook.com/tr?id=870188824858773&ev=PageView&noscript=1"
      />
    </noscript>
    <!-- quasar:entry-point -->
  </body>
</html>
