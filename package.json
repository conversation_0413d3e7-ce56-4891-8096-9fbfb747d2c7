{"name": "vuejs.quasar.micro-route", "version": "0.0.1", "description": "A modern Vue.js project built with Quasar Framework, Pinia state management, and Micro-Route architecture.", "productName": "VueJS App with Micro-Route Architecture", "author": "danh<PERSON><PERSON><PERSON> <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "postinstall": "quasar prepare"}, "dependencies": {"@deck.gl/core": "^9.1.12", "@deck.gl/mapbox": "^9.1.12", "@quasar/extras": "^1.16.4", "@rive-app/canvas": "^2.29.0", "@tailwindcss/postcss": "^4.1.6", "@tanstack/vue-query": "^5.76.0", "@turf/bbox": "^7.2.0", "@turf/bbox-polygon": "^7.2.0", "@turf/boolean-point-in-polygon": "^7.2.0", "@turf/circle": "^7.2.0", "@turf/distance": "^7.2.0", "@turf/helpers": "^7.2.0", "@turf/intersect": "^7.2.0", "@turf/transform-rotate": "^7.2.0", "@turf/transform-translate": "^7.2.0", "@types/ngeohash": "^0.6.8", "@vueuse/core": "^13.3.0", "@zxing/library": "^0.21.3", "axios": "^1.2.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "gsap": "^3.12.7", "howler": "^2.2.4", "libphonenumber-js": "^1.12.9", "lodash": "^4.17.21", "maplibre-gl": "^5.5.0", "ngeohash": "^0.6.3", "numeral": "^2.0.6", "pinia": "^3.0.1", "pixi.js": "^8.11.0", "plyr": "^3.7.8", "postcss": "^8.5.3", "qrcode.vue": "^3.6.0", "quasar": "^2.16.0", "socket.io-client": "^4.8.1", "swiper": "^11.2.7", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.6", "ua-parser-js": "^2.0.3", "vee-validate": "^4.15.0", "vue": "^3.5.14", "vue-i18n": "^11.0.0", "vue-router": "^4.0.12", "vue-turnstile": "^1.0.11", "vue3-maplibre-gl": "^3.2.4", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.14.0", "@intlify/unplugin-vue-i18n": "^4.0.0", "@quasar/app-vite": "^2.1.0", "@types/crypto-js": "^4.2.2", "@types/gsap": "^3.0.0", "@types/howler": "^2.2.12", "@types/lodash": "^4.17.17", "@types/maplibre-gl": "^1.14.0", "@types/node": "^22.15.21", "@types/numeral": "^2.0.5", "@types/pixi.js": "^5.0.0", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.4.0", "autoprefixer": "^10.4.2", "crypto-js": "^4.2.0", "eslint": "^9.14.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "prettier": "^3.3.3", "typescript": "^5.8.3", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vite-plugin-checker": "^0.9.0", "vue-tsc": "^2.0.29"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}