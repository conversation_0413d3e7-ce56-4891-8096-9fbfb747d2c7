import { Assets, Application, AnimatedSprite, Cache } from 'pixi.js';

const DEFAULT_ANIMATION_SPEED = 0.5;
const DEFAULT_RESOLUTION_MULTIPLIER = 2;
const ASSETS_BASE_PATH = 'anims';

interface IOptions {
  id: HTMLCanvasElement;
  name: string;
  json: string;
  animationSpeed?: number;
  size?: 'cover' | 'contain';
  width?: number;
  height?: number;
}

interface IPixiAnimationReturn {
  app: Application;
  clearCache: () => void;
  destroy: () => void;
}

export async function usePixiAnimations(options: IOptions): Promise<IPixiAnimationReturn> {
  let app: Application | null = null;
  let sprite: AnimatedSprite | null = null;

  // Initialize PIXI Application
  app = await createApplication(options);

  // Load and create animated sprite
  const assetPath = `${ASSETS_BASE_PATH}/${options.json}`;
  sprite = await createAnimatedSprite(assetPath, options.name);

  // Configure sprite
  configureSprite(sprite, app, options);

  // Add to stage
  app.stage.addChild(sprite);

  return {
    app,
    clearCache: clearAssetCache,
    destroy: () => destroyResources(app, sprite),
  };
}

async function createApplication(options: IOptions): Promise<Application> {
  const app = new Application();

  await app.init({
    width: options.width || window.innerWidth,
    height: options.height || window.innerHeight,
    antialias: true,
    autoDensity: true,
    backgroundAlpha: 0,
    resolution: Math.max(window.devicePixelRatio, DEFAULT_RESOLUTION_MULTIPLIER),
    canvas: options.id,
  });

  return app;
}

async function createAnimatedSprite(
  assetPath: string,
  animationName: string,
): Promise<AnimatedSprite> {
  clearAssetCache();

  const spritesheet = await Assets.load(assetPath);
  Cache.set(assetPath, spritesheet);

  if (!spritesheet.animations[animationName]) {
    throw new Error(`Animation "${animationName}" not found in spritesheet`);
  }

  return new AnimatedSprite(spritesheet.animations[animationName]);
}

function configureSprite(sprite: AnimatedSprite, app: Application, options: IOptions): void {
  // Calculate and apply scaling
  const scale = calculateSpriteScale(sprite, app, options.size);
  sprite.scale.set(scale);

  // Center the sprite
  sprite.anchor.set(0.5, 0.5);
  sprite.position.set(app.screen.width / 2, app.screen.height / 2);

  // Configure animation
  sprite.animationSpeed = options.animationSpeed ?? DEFAULT_ANIMATION_SPEED;
  sprite.play();
}

function calculateSpriteScale(
  sprite: AnimatedSprite,
  app: Application,
  sizeMode: 'cover' | 'contain' = 'contain',
): number {
  const textureRatio = sprite.texture.width / sprite.texture.height;
  const screenRatio = app.screen.width / app.screen.height;

  const shouldScaleByWidth =
    (textureRatio >= screenRatio && sizeMode === 'contain') ||
    (textureRatio < screenRatio && sizeMode === 'cover');

  return shouldScaleByWidth
    ? app.screen.width / sprite.texture.width
    : app.screen.height / sprite.texture.height;
}

function clearAssetCache(): void {
  Assets.cache.reset();
}

function destroyResources(app: Application | null, sprite: AnimatedSprite | null): void {
  if (sprite) {
    sprite.stop();
    sprite.destroy();
  }

  if (app) {
    app.destroy(true);
  }
}
