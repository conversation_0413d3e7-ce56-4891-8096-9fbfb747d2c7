import { useMapStore } from '@stores';
import {
  EaseToOptions,
  FitBoundsOptions,
  FlyToOptions,
  LngLatBoundsLike,
  LngLatLike,
} from 'maplibre-gl';

export function useMapHelper() {
  const storeMap = useMapStore();

  const { mapInstance } = storeToRefs(storeMap);

  function mapFlyTo(options: FlyToOptions): Promise<void> {
    return new Promise((resolve) => {
      if (!mapInstance.value) return resolve();
      mapInstance.value.flyTo(options);
      void mapInstance.value.once('moveend', () => resolve());
    });
  }

  function mapPanTo(lnglat: LngLatLike, options?: EaseToOptions): Promise<void> {
    return new Promise((resolve) => {
      if (!mapInstance.value) return resolve();
      mapInstance.value.panTo(lnglat, options);
      void mapInstance.value.once('moveend', () => resolve());
    });
  }

  function mapFitBounds(bounds: LngLatBoundsLike, options?: FitBoundsOptions): Promise<void> {
    return new Promise((resolve) => {
      if (!mapInstance.value) return resolve();
      mapInstance.value.fitBounds(bounds, options);
      void mapInstance.value.once('moveend', () => resolve());
    });
  }

  return {
    mapFlyTo,
    mapPanTo,
    mapFitBounds,
  };
}
