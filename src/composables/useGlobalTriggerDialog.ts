import { useMapStore } from '@stores';
import { delay } from '@utils';

interface DialogTrigger {
  dialogPath: string;
  props?: Record<string, unknown>;
  condition: () => boolean;
  action?: () => Promise<void>;
}

interface TriggerState {
  triggeredIds: Set<string>;
  isProcessing: boolean;
  pendingTrigger: boolean;
}

export function useGlobalTriggerDialog() {
  const SUBSEQUENT_TRIGGER_DELAY = 200;

  const storeMap = useMapStore();

  const { isLoading } = storeToRefs(storeMap);
  const { activePage, activeDialog, openDialog, push } = useMicroRoute();
  const { redirectedFrom } = useRoute();

  const state = reactive<TriggerState>({
    triggeredIds: new Set(),
    isProcessing: false,
    pendingTrigger: false,
  });

  const isIdleInHome = computed(() => {
    const isHomeScreen = activePage.value === 'home';
    const noDialog = !activeDialog.value;
    if (isLoading.value) return false;
    return [isHomeScreen, noDialog].every(Boolean);
  });

  // Registry of dialog triggers: priority is determined by the order of the array
  const triggers = computed<DialogTrigger[]>(() => {
    const recoverToken = (): string => {
      if (!redirectedFrom) return '';
      if (!redirectedFrom.query?.recover_token) return '';
      return String(redirectedFrom.query.recover_token);
    };

    const topUpStatus = (): string => {
      if (!redirectedFrom) return '';
      if (!redirectedFrom.query?.status) return '';
      return String(redirectedFrom.query.status);
    };

    const topUpId = (): string => {
      if (!redirectedFrom) return '';
      if (!redirectedFrom.query?.reference) return '';
      return String(redirectedFrom.query.reference);
    };

    return [
      {
        dialogPath: 'sqkii_vouchers_recover_password',
        props: {
          recover_token: recoverToken(),
        },
        condition: () => Boolean(recoverToken()),
        action: async () => {
          push('sqkii_vouchers');
          await delay(1000);
        },
      },
      {
        dialogPath: 'sqkii_vouchers_top_up_result',
        props: {
          status: topUpStatus(),
          id: topUpId(),
        },
        condition: () => Boolean(topUpStatus()) && Boolean(topUpId()),
        action: async () => {
          push('sqkii_vouchers');
          await delay(1000);
        },
      },
    ];
  });

  const getId = (trigger: DialogTrigger): string => trigger.dialogPath;

  const canTrigger = (trigger: DialogTrigger): boolean => {
    const id = getId(trigger);
    return !state.triggeredIds.has(id) && trigger.condition();
  };

  const triggerDialog = async (trigger: DialogTrigger): Promise<void> => {
    if (trigger.action) await trigger.action();
    openDialog(trigger.dialogPath, trigger.props);
  };

  const executeTrigger = async (trigger: DialogTrigger): Promise<void> => {
    const id = getId(trigger);
    state.triggeredIds.add(id);
    await triggerDialog(trigger);
  };

  const evaluateTriggers = async (): Promise<void> => {
    if (state.isProcessing) return;

    if (!isIdleInHome.value && !state.pendingTrigger) return;

    state.isProcessing = true;
    state.pendingTrigger = false;

    try {
      const sortedTriggers = triggers.value
        .map((trigger, index) => ({ trigger, index }))
        .sort((a, b) => a.index - b.index)
        .map((item) => item.trigger);

      for (const trigger of sortedTriggers) {
        if (canTrigger(trigger)) {
          await executeTrigger(trigger);
          break;
        }
      }
    } finally {
      state.isProcessing = false;
    }
  };

  const handleDelayedTrigger = async (ms: number): Promise<void> => {
    await delay(ms);
    await evaluateTriggers();
  };

  watch(
    isIdleInHome,
    async (isIdle) => {
      if (!isIdle) return;

      await handleDelayedTrigger(SUBSEQUENT_TRIGGER_DELAY);
    },
    { immediate: true },
  );

  watch(
    () => state.pendingTrigger,
    async (isPending) => {
      if (isPending) await evaluateTriggers();
    },
  );
}
