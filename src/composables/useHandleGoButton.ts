import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useBAStore } from '@stores';
import { useFetchQueries, useNow, usePageTracker } from '@composables';
import { useMicroRoute } from '@core/MicroRouter/composables/useMicroRouter';
import { BrandAction, ActivatingTimeMission, BrandActionType } from '@types';
import { useOfferWallVisitWebMutation } from '@services';

type ActionFrom = 'mission_timed' | 'brand_action';
type PromoType = 'promocode' | 'barcode';

interface BrandActionHandler {
  handle(ba: BrandAction): Promise<void> | void;
}

const ROUTES = {
  BA_INSTRUCTION: 'ba_instruction',
  OFFER_WALL_BA_INSTRUCTION: 'offer_wall/ba_instruction',
  SQKII_VOUCHERS: 'sqkii_vouchers',
  MISSIONS: 'missions',
} as const;

const DIALOGS = {
  ENTER_PROMO: 'enter_promo',
  BA_STATUS: 'ba_status',
  BA_SURVEY: 'ba_survey',
  SPF_WELCOME: 'spf_welcome',
} as const;

const SPECIAL_UNIQUE_IDS = {
  SPF_4: 'spf_4',
} as const;

export function useHandleGoButton(from: ActionFrom = 'brand_action') {
  const visitWebMutation = useOfferWallVisitWebMutation();
  const storeBA = useBAStore();
  const now = useNow();

  const { push, openDialog, activePage } = useMicroRoute();
  const { user_brand_actions } = storeToRefs(storeBA);
  const { offerWallQuery } = useFetchQueries();
  const { tracker } = usePageTracker();

  const isOfferWallPage = computed(() => activePage.value === 'offer_wall');

  const instructionRoute = computed(() =>
    isOfferWallPage.value ? ROUTES.BA_INSTRUCTION : ROUTES.OFFER_WALL_BA_INSTRUCTION,
  );

  function handlePromoAction(item: BrandAction, type: PromoType = 'promocode'): void {
    const hasInstructions = item.instructions?.every(Boolean);

    if (hasInstructions) push(instructionRoute.value, { data: item });
    else openDialog(DIALOGS.ENTER_PROMO, { data: item, type });
  }

  function isLockedItem(item: BrandAction): boolean {
    if (item.status !== 'new') return true;

    const brandActionsMap = new Map(
      user_brand_actions.value
        .filter((brand) => brand.lock_until && +new Date(brand.lock_until) >= now.value)
        .map((brand) => [brand.ba_unique_id, brand]),
    );

    return Boolean(brandActionsMap.get(item.unique_id));
  }

  async function handleVisitWebAction(item: BrandAction | ActivatingTimeMission): Promise<void> {
    try {
      const id = 'brandAction' in item ? item.brandAction._id : item._id;
      await visitWebMutation.mutateAsync(id);
      await offerWallQuery.refetch();
    } catch (error) {
      console.error('Error handling visit web action:', error);
    }
  }

  function trackBrandActionClick(ba: BrandAction): void {
    tracker({
      id: 'offer_wall_action',
      action: 'click',
      data: {
        type: ba.type,
        brand_action_id: ba._id,
        ba_unique_id: ba.ba_unique_id,
        status: ba.status,
        from,
      },
    });
  }

  function createInstructionHandler(): BrandActionHandler {
    return {
      handle(ba: BrandAction) {
        push(instructionRoute.value, { data: ba });
      },
    };
  }

  function createPromoHandler(type: PromoType = 'promocode'): BrandActionHandler {
    return {
      handle(ba: BrandAction) {
        handlePromoAction(ba, type);
      },
    };
  }

  function createWebHandler(): BrandActionHandler {
    return {
      async handle(ba: BrandAction) {
        if (ba.unique_id === SPECIAL_UNIQUE_IDS.SPF_4) push(instructionRoute.value, { data: ba });
        else await handleVisitWebAction(ba);
      },
    };
  }

  function createDialogHandler(dialogName: string): BrandActionHandler {
    return {
      handle(ba: BrandAction) {
        openDialog(dialogName, { ba });
      },
    };
  }

  function createRouteHandler(route: string): BrandActionHandler {
    return {
      handle() {
        push(route);
      },
    };
  }

  function createSPFHandler(skipQuiz: boolean): BrandActionHandler {
    return {
      handle(ba: BrandAction) {
        openDialog(DIALOGS.SPF_WELCOME, {
          skipQuiz: skipQuiz || from === 'mission_timed',
          idFromOfferWall: ba.group_id,
        });
      },
    };
  }

  const actionHandlers = new Map<BrandActionType, BrandActionHandler>([
    ['receipt_verification', createInstructionHandler()],
    ['client_verify', createInstructionHandler()],
    ['scan_qrcode', createInstructionHandler()],
    ['promo_code', createPromoHandler('promocode')],
    ['enter_promo_code', createPromoHandler('promocode')],
    ['enter_barcode', createPromoHandler('barcode')],
    ['open_external_link', createWebHandler()],
    ['visit_web', createWebHandler()],
    ['survey', createDialogHandler(DIALOGS.BA_SURVEY)],
    ['sqkii_voucher', createRouteHandler(ROUTES.SQKII_VOUCHERS)],
    ['use_sqkii_voucher', createRouteHandler(ROUTES.SQKII_VOUCHERS)],
    ['spf_quiz', createSPFHandler(false)],
    ['read_spf_message', createSPFHandler(true)],
    ['spf_sharing', createSPFHandler(true)],
    ['location_based', createRouteHandler(ROUTES.MISSIONS)],
  ]);

  async function handleGoButton(ba: BrandAction): Promise<void> {
    trackBrandActionClick(ba);

    const lockedItem = isLockedItem(ba);
    if (lockedItem) {
      openDialog(DIALOGS.BA_STATUS, { brandAction: lockedItem });
      return;
    }

    const handler = actionHandlers.get(ba.type);

    if (handler) await handler.handle(ba);
    else {
      console.warn(`Brand action type '${ba.type}' is not implemented yet`);
      alert(`${ba.type} Not implemented yet`);
    }
  }

  return {
    handleGoButton,
    isLockedItem,
    handleVisitWebAction,
  };
}
