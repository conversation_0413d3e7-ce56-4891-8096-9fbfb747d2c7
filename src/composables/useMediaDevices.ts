import { STORAGE_KEYS } from '@enums';

interface MediaSize {
  width: number;
  height: number;
}

interface UseMediaDevicesConfig {
  video?: MediaTrackConstraints | boolean;
  audio?: MediaTrackConstraints | boolean;
}

interface UseMediaDevicesCallbacks {
  allowed?: (stream: MediaStream, size: MediaSize) => void;
  blocked?: () => void;
}

interface MediaTrackConstraintSetWithTorch extends MediaTrackConstraintSet {
  torch?: boolean;
}

interface LegacyNavigator extends Navigator {
  getUserMedia?: (
    constraints: MediaStreamConstraints,
    successCallback: (stream: MediaStream) => void,
    errorCallback: (error: unknown) => void,
  ) => void;
  webkitGetUserMedia?: (
    constraints: MediaStreamConstraints,
    successCallback: (stream: MediaStream) => void,
    errorCallback: (error: unknown) => void,
  ) => void;
  mozGetUserMedia?: (
    constraints: MediaStreamConstraints,
    successCallback: (stream: MediaStream) => void,
    errorCallback: (error: unknown) => void,
  ) => void;
}

export function useMediaDevices(
  config: UseMediaDevicesConfig,
  allowed?: UseMediaDevicesCallbacks['allowed'],
  blocked?: UseMediaDevicesCallbacks['blocked'],
) {
  const showAllowAccess = ref(false);
  const showBlockedAccess = ref(false);
  const showScan = ref(false);
  const isAllowed = LocalStorage.getItem(STORAGE_KEYS.CAMERA_PERMISSION);

  let mediaTrack: MediaStreamTrack | null = null;
  let isFlashOn = false;

  initializeMediaDevicesPolyfill();

  function toggleFlash(): void {
    if (!mediaTrack) return;

    isFlashOn = !isFlashOn;
    mediaTrack
      .applyConstraints({
        advanced: [{ torch: isFlashOn } as MediaTrackConstraintSetWithTorch],
      })
      .catch((error) => {
        console.warn('Flash toggle failed:', error);
      });
  }

  async function requestMediaAccess(): Promise<void> {
    showAllowAccess.value = false;

    try {
      const stream = await navigator.mediaDevices.getUserMedia(config);
      const videoTrack = stream.getVideoTracks()[0];

      if (!videoTrack) {
        throw new Error('No video track available');
      }

      const settings = videoTrack.getSettings();
      const size: MediaSize = {
        width: settings.width || 0,
        height: settings.height || 0,
      };

      mediaTrack = videoTrack;

      LocalStorage.set(STORAGE_KEYS.CAMERA_PERMISSION, true);
      showBlockedAccess.value = false;
      showScan.value = true;

      allowed?.(stream, size);
    } catch (error) {
      console.error('Media access denied or failed:', error);

      showBlockedAccess.value = true;
      LocalStorage.set(STORAGE_KEYS.CAMERA_PERMISSION, false);

      blocked?.();
    }
  }

  return {
    showScan,
    showBlockedAccess,
    showAllowAccess,
    isAllowed,

    preRequest: requestMediaAccess,
    request: requestMediaAccess,
    onUpdateFlash: toggleFlash,
  };
}

function initializeMediaDevicesPolyfill(): void {
  if (!navigator.mediaDevices) {
    (navigator as any).mediaDevices = {};
  }

  if (!navigator.mediaDevices.getUserMedia) {
    const legacyNav = navigator as LegacyNavigator;
    const getUserMedia =
      legacyNav.getUserMedia || legacyNav.webkitGetUserMedia || legacyNav.mozGetUserMedia;

    if (!getUserMedia) {
      navigator.mediaDevices.getUserMedia = () =>
        Promise.reject(new Error('getUserMedia is not supported in this browser'));
      return;
    }

    navigator.mediaDevices.getUserMedia = (constraints: MediaStreamConstraints) => {
      return new Promise<MediaStream>((resolve, reject) => {
        getUserMedia.call(navigator, constraints, resolve, reject);
      });
    };
  }
}
