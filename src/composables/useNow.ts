export interface UseNowOptions {
  /**
   * Update interval in milliseconds
   * @default 1000
   */
  interval?: number;
  /**
   * Expose more controls
   * @default false
   */
  controls?: boolean;
}

export function useNow(options: UseNowOptions & { controls: true }): {
  now: Ref<number>;
  pause: () => void;
  resume: () => void;
  isActive: Ref<boolean>;
};

export function useNow(options?: UseNowOptions): Ref<number>;

export function useNow(options: UseNowOptions = {}) {
  const { interval = 1000, controls = false } = options;

  const now = ref(+new Date());
  const isActive = ref(true);
  let timer: ReturnType<typeof setInterval> | null = null;

  function update(): void {
    now.value = +new Date();
  }

  function pause(): void {
    isActive.value = false;
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  }

  function resume(): void {
    if (!isActive.value) {
      isActive.value = true;
      update();
      timer = setInterval(update, interval);
    }
  }

  // Start the timer
  timer = setInterval(update, interval);

  onUnmounted(() => {
    pause();
  });

  if (controls) {
    return {
      now: readonly(now),
      pause,
      resume,
      isActive: readonly(isActive),
    };
  }

  return readonly(now);
}
