import { QueryKey } from '@tanstack/query-core';
import { UseQueryOptions } from '@tanstack/vue-query';
import { APIResponseError, APISVResponseError } from '@types';
import { AxiosRequestConfig } from 'axios';

export type QueryOptions<
  TQueryFnData = unknown,
  TError = APIResponseError,
  TData = TQueryFnData,
  TQueryData = TQueryFnData,
  T<PERSON><PERSON>y<PERSON><PERSON> extends QueryKey = QueryKey,
> = UseQueryOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>;

export type QuerySVOptions<
  TQueryFnData = unknown,
  TError = APISVResponseError,
  TData = TQueryFnData,
  TQueryData = TQueryFnData,
  TQ<PERSON>y<PERSON><PERSON> extends QueryKey = QueryKey,
> = UseQueryOptions<TQueryFnData, TError, <PERSON><PERSON>, TQueryData, TQueryKey>;

export type ApiService = 'HTM' | 'SV';

export interface ServiceConstructorData {
  path: string;
  service: ApiService;
}

export interface HMACSignatureData {
  sig: string;
  ctime: number;
  country?: string;
  'x-sign-key'?: string;
}

export interface VoucherHeaders {
  country: string;
  'x-sign-key': string;
}

export interface ApiRequestConfig extends AxiosRequestConfig {
  customHeaders?: Record<string, string>;
}
