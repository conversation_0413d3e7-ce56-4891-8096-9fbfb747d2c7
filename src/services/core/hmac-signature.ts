import { InternalAxiosRequestConfig } from 'axios';
import Base64 from 'crypto-js/enc-base64';
import HmacSHA256 from 'crypto-js/hmac-sha256';
import { HMACSignatureData, VoucherHeaders } from '@services';

export class HMACSignatureGenerator {
  private static generateTimestamp(): number {
    return Date.now();
  }

  private static normalizeUrl(url: string): string {
    return url.startsWith('/') ? url : `/${url}`;
  }

  private static createSignature(stringToSign: string, secret: string): string {
    return Base64.stringify(HmacSHA256(stringToSign, secret));
  }

  private static getHmacSecret(): string {
    const secret = process.env.APP_HMAC_SECRET;
    if (!secret) {
      throw new Error('APP_HMAC_SECRET environment variable is not set');
    }
    return secret;
  }

  private static isVoucherService(config: InternalAxiosRequestConfig): boolean {
    const serviceType = (config as any).serviceType;
    if (serviceType) return serviceType === 'SV';
    const baseURL = String(config.baseURL);
    const voucherEndpoint = `${process.env.APP_SV_END_POINT}/${process.env.APP_SV_API_PREFIX}`;
    return baseURL === voucherEndpoint;
  }

  static generateSignature(config: InternalAxiosRequestConfig): HMACSignatureData {
    return this.isVoucherService(config)
      ? this.generateVoucher(config)
      : this.generateStandard(config);
  }

  static generateStandard(config: InternalAxiosRequestConfig): HMACSignatureData {
    const path = this.normalizeUrl(config.url || '');
    const method = config.method?.toUpperCase() || '';
    const contentType = config.headers['Content-Type'] as string;
    const ctime = this.generateTimestamp();

    const stringToSign = [method, contentType, ctime, path].join('\n');
    const sig = this.createSignature(stringToSign, this.getHmacSecret());

    return { sig, ctime };
  }

  static generateVoucher(config: InternalAxiosRequestConfig): HMACSignatureData {
    const voucherHeaders: VoucherHeaders = {
      country: 'SG',
      'x-sign-key': process.env.APP_SV_SIGN_KEY || '',
    };

    // Set headers on config
    Object.assign(config.headers, voucherHeaders);

    const path = this.normalizeUrl(config.url || '');
    const method = config.method?.toUpperCase() || '';
    const contentType = config.headers['Content-Type'] as string;
    const ctime = this.generateTimestamp();
    const { country, 'x-sign-key': xSignKey } = voucherHeaders;

    const stringToSign = [method, contentType, ctime, country, path].join('\n');
    const sig = this.createSignature(stringToSign, this.getHmacSecret());

    return {
      sig,
      ctime,
      country,
      'x-sign-key': xSignKey,
    };
  }
}
