import { AxiosInstance, AxiosError } from 'axios';
import { APIResponseError, APISVResponseError } from '@types';
import { ApiService, HeadersUtils } from '@services';

export class ApiInterceptors {
  static setupRequestInterceptor(instance: AxiosInstance, service: ApiService): void {
    instance.interceptors.request.use(
      (config) => {
        config.headers = HeadersUtils.setAuthHeaders(config);
        HeadersUtils.addAuthorizationHeader(config, service);
        return config;
      },
      (error) => Promise.reject(error instanceof Error ? error : new Error(String(error))),
    );
  }

  static setupResponseInterceptor(instance: AxiosInstance): void {
    instance.interceptors.response.use(
      (response) => {
        if (response.data.status === 'success') return response.data;
        if (response.data.error_code === 401) {
          LocalStorage.clear();
          window.location.reload();
        }

        const errorData: APIResponseError = response.data;
        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
        return Promise.reject<APIResponseError>(errorData);
      },
      (error: AxiosError<APIResponseError>) => {
        const errorData = error.response?.data;
        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
        return Promise.reject<APIResponseError>(errorData);
      },
    );
  }

  static setupSVResponseInterceptor(instance: AxiosInstance): void {
    instance.interceptors.response.use(
      (response) => {
        if (response.data.status === 'success') return response.data;
        if (response.data.error_code === 401) {
          LocalStorage.clear();
          window.location.reload();
        }

        const errorData: APISVResponseError = response.data;
        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
        return Promise.reject<APISVResponseError>(errorData);
      },
      (error: AxiosError<APISVResponseError>) => {
        const errorData = error.response?.data;
        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
        return Promise.reject<APISVResponseError>(errorData);
      },
    );
  }
}
