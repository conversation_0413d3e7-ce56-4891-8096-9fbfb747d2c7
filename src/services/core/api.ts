import axios, { AxiosInstance } from 'axios';
import { ServiceConstructorData, ApiRequestConfig, ApiInterceptors, ApiService } from '@services';

export class Api {
  private http: AxiosInstance;
  private readonly path: string;
  private readonly service: ApiService;
  private readonly baseURL: string;

  constructor(config: ServiceConstructorData) {
    const { path, service = 'HTM' } = config;
    this.path = path;
    this.service = service;
    this.baseURL = this.determineBaseService(service);
    this.http = this.createAxiosInstance();
    this.setupInterceptors(service);
  }

  private determineBaseService(service: ApiService): string {
    switch (service) {
      case 'SV':
        return `${process.env.APP_SV_END_POINT}/${process.env.APP_SV_API_PREFIX}`;
      case 'HTM':
      default:
        return `${process.env.APP_END_POINT}/${process.env.APP_API_PREFIX}`;
    }
  }

  private createAxiosInstance(): AxiosInstance {
    return axios.create({
      baseURL: this.baseURL,
      url: this.path,
      headers: {
        'Content-Type': 'application/json',
        Accept: '*/*',
      },
      withCredentials: true,
    });
  }

  private setupInterceptors(service: ApiService): void {
    ApiInterceptors.setupRequestInterceptor(this.http, service);
    if (service === 'HTM') ApiInterceptors.setupResponseInterceptor(this.http);
    else if (service === 'SV') ApiInterceptors.setupSVResponseInterceptor(this.http);
  }

  private makeRequest<T>(
    method: 'get' | 'post' | 'put' | 'patch' | 'delete',
    config: ApiRequestConfig = {},
  ) {
    const { url = this.path, data, customHeaders, ...requestConfig } = config;
    const finalConfig = customHeaders
      ? { ...requestConfig, headers: { ...requestConfig.headers, ...customHeaders } }
      : requestConfig;

    switch (method) {
      case 'get':
      case 'delete':
        return this.http[method]<T>(url, finalConfig);
      default:
        return this.http[method]<T>(url, data, finalConfig);
    }
  }

  get<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('get', config);
  }

  post<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('post', config);
  }

  postFormData<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('post', {
      ...config,
      customHeaders: { 'Content-Type': 'multipart/form-data' },
    });
  }
  postFormUrlencoded<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('post', {
      ...config,
      customHeaders: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
  }

  put<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('put', config);
  }

  putFormData<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('put', {
      ...config,
      customHeaders: { 'Content-Type': 'multipart/form-data' },
    });
  }

  putFormUrlencoded<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('put', {
      ...config,
      customHeaders: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
  }

  patch<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('patch', config);
  }

  delete<T>(config: ApiRequestConfig = {}) {
    return this.makeRequest<T>('delete', config);
  }

  getServiceType(): ApiService {
    return this.service;
  }
}
