import { InternalAxiosRequestConfig, AxiosRequestHeaders } from 'axios';
import { ApiService, HMACSignatureGenerator } from '@services';
import { STORAGE_KEYS } from '@enums';

export class HeadersUtils {
  static setAuthHeaders(config: InternalAxiosRequestConfig): AxiosRequestHeaders {
    const signatureData = HMACSignatureGenerator.generateSignature(config);
    return { ...config.headers, ...signatureData } as unknown as AxiosRequestHeaders;
  }

  static addAuthorizationHeader(config: InternalAxiosRequestConfig, service: ApiService): void {
    const token = LocalStorage.getItem(
      service === 'HTM' ? STORAGE_KEYS.HTM_TOKEN : STORAGE_KEYS.SV_TOKEN,
    ) as string;
    if (token) config.headers.authorization = `Bearer ${token}`;
  }
}
