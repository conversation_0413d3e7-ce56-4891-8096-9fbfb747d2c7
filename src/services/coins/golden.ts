import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useMutation, useQuery } from '@tanstack/vue-query';
import { APIResponseError, DiscountPrice, GoldenEliminatedPayload, GoldenEliminated } from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/golden',
  service: 'HTM',
};

export class GoldenModel extends Model {
  static geohashes() {
    return this.api.get<string[]>({
      url: `${this.path}/geohashes`,
    });
  }

  static eliminated() {
    return this.api.get<GoldenEliminated>({
      url: `${this.path}/eliminated`,
    });
  }

  static use(data: GoldenEliminatedPayload) {
    return this.api.post<string[]>({
      url: `${this.path}/use-eliminate-pu`,
      data,
    });
  }

  static price(data: GoldenEliminatedPayload) {
    return this.api.post<DiscountPrice>({
      url: `${this.path}/eliminate-price`,
      data,
    });
  }
}

GoldenModel.setup(modelConfig);

export const GOLDEN_GEOHASHES_QUERY = 'GOLDEN_GEOHASHES_QUERY';
export function useGoldenGeohashesQuery(options: Partial<QueryOptions<string[]>> = {}) {
  return useQuery<string[], APIResponseError>({
    queryKey: [GOLDEN_GEOHASHES_QUERY],
    queryFn: () => GoldenModel.geohashes().then((r) => r.data),
    ...options,
  });
}

export const GOLDEN_ELIMINATED_QUERY = 'GOLDEN_ELIMINATED_QUERY';
export function useGoldenEliminatedQuery(options: Partial<QueryOptions<GoldenEliminated>> = {}) {
  return useQuery<GoldenEliminated, APIResponseError>({
    queryKey: [GOLDEN_ELIMINATED_QUERY],
    queryFn: () => GoldenModel.eliminated().then((r) => r.data),
    ...options,
  });
}

export const GOLDEN_USE_MUTATION = 'GOLDEN_USE_MUTATION';
export function useGoldenUseMutation() {
  return useMutation<string[], APIResponseError, GoldenEliminatedPayload>({
    mutationKey: [GOLDEN_USE_MUTATION],
    mutationFn: (data) => GoldenModel.use(data).then((r) => r.data),
  });
}

export const GOLDEN_PRICE_MUTATION = 'GOLDEN_PRICE_MUTATION';
export function useGoldenPriceMutation() {
  return useMutation<DiscountPrice, APIResponseError, GoldenEliminatedPayload>({
    mutationKey: [GOLDEN_PRICE_MUTATION],
    mutationFn: (data) => GoldenModel.price(data).then((r) => r.data),
  });
}
