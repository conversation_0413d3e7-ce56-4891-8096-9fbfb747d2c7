import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useMutation, useQuery } from '@tanstack/vue-query';
import {
  APIResponseError,
  RetrieveSilverCoin,
  DiscountPrice,
  ShrinkSilverCoin,
  ShrinkSilverCoinPayload,
  SilverCoin,
  SilverCoinVerification,
  SilverWinnerInfo,
  WinnerInfoPayload,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/silver',
  service: 'HTM',
};

export class SilverModel extends Model {
  static get() {
    return this.api.get<SilverCoin[]>({
      url: this.path,
    });
  }

  static retrieve(id: string) {
    return this.api.get<RetrieveSilverCoin>({
      url: `${this.path}/${id}`,
    });
  }

  static shrink(data: ShrinkSilverCoinPayload) {
    return this.api.post<ShrinkSilverCoin>({
      url: `${this.path}/shrink`,
      data,
    });
  }

  static price(data: ShrinkSilverCoinPayload) {
    return this.api.post<DiscountPrice>({
      url: `${this.path}/shrink-price`,
      data,
    });
  }

  static verifySerialNumber(serial_number: string) {
    return this.api.post<SilverCoinVerification>({
      url: `${this.path}/verify`,
      data: { serial_number },
    });
  }

  static verifications() {
    return this.api.get<SilverCoinVerification[]>({
      url: `${this.path}/verifications`,
    });
  }

  static verifyWinner(data: Partial<WinnerInfoPayload>) {
    return this.api.post<SilverCoinVerification>({
      url: `${this.path}/verify`,
      data,
    });
  }

  static submittedDetails(id: string) {
    return this.api.post<SilverWinnerInfo>({
      url: `${this.path}/submitted-details`,
      data: { id },
    });
  }
}

SilverModel.setup(modelConfig);

export const SILVER_QUERY = 'SILVER_QUERY';
export function useSilverQuery(options: Partial<QueryOptions<SilverCoin[]>> = {}) {
  return useQuery<SilverCoin[], APIResponseError>({
    queryKey: [SILVER_QUERY],
    queryFn: () => SilverModel.get().then((r) => r.data),
    ...options,
  });
}

export const SILVER_RETRIEVE_QUERY = 'SILVER_RETRIEVE_QUERY';
export function useSilverRetrieveQuery(
  id: string,
  options: Partial<QueryOptions<RetrieveSilverCoin>> = {},
) {
  return useQuery<RetrieveSilverCoin, APIResponseError>({
    queryKey: [SILVER_RETRIEVE_QUERY, id],
    queryFn: () => SilverModel.retrieve(id).then((r) => r.data),
    ...options,
  });
}

export const SILVER_SHRINK_MUTATION = 'SILVER_SHRINK_MUTATION';
export function useSilverShrinkMutation() {
  return useMutation<ShrinkSilverCoin, APIResponseError, ShrinkSilverCoinPayload>({
    mutationKey: [SILVER_SHRINK_MUTATION],
    mutationFn: (data: ShrinkSilverCoinPayload) => SilverModel.shrink(data).then((r) => r.data),
  });
}

export const SILVER_PRICE_MUTATION = 'SILVER_PRICE_MUTATION';
export function useSilverPriceMutation() {
  return useMutation<DiscountPrice, APIResponseError, ShrinkSilverCoinPayload>({
    mutationKey: [SILVER_PRICE_MUTATION],
    mutationFn: (data: ShrinkSilverCoinPayload) => SilverModel.price(data).then((r) => r.data),
  });
}

export const SILVER_VERIFY_SERIAL_NUMBER_MUTATION = 'SILVER_VERIFY_SERIAL_NUMBER_MUTATION';
export function useSilverVerifySerialNumberMutation() {
  return useMutation<SilverCoinVerification, APIResponseError, string>({
    mutationKey: [SILVER_VERIFY_SERIAL_NUMBER_MUTATION],
    mutationFn: (serial_number) =>
      SilverModel.verifySerialNumber(serial_number).then((r) => r.data),
  });
}

export const SILVER_VERIFICATIONS_QUERY = 'SILVER_VERIFICATIONS_QUERY';
export function useSilverVerificationsQuery(
  options: Partial<QueryOptions<SilverCoinVerification[]>> = {},
) {
  return useQuery<SilverCoinVerification[], APIResponseError>({
    queryKey: [SILVER_VERIFICATIONS_QUERY],
    queryFn: () => SilverModel.verifications().then((r) => r.data),
    ...options,
  });
}

export const SILVER_VERIFY_WINNER_MUTATION = 'SILVER_VERIFY_WINNER_MUTATION';
export function useSilverVerifyWinnerMutation() {
  return useMutation<SilverCoinVerification, APIResponseError, Partial<WinnerInfoPayload>>({
    mutationKey: [SILVER_VERIFY_WINNER_MUTATION],
    mutationFn: (data) => SilverModel.verifyWinner(data).then((r) => r.data),
  });
}

export const SILVER_SUBMITTED_DETAILS_MUTATION = 'SILVER_SUBMITTED_DETAILS_MUTATION';
export function useSilverSubmittedDetailsMutation() {
  return useMutation<SilverWinnerInfo, APIResponseError, string>({
    mutationKey: [SILVER_SUBMITTED_DETAILS_MUTATION],
    mutationFn: (id) => SilverModel.submittedDetails(id).then((r) => r.data),
  });
}
