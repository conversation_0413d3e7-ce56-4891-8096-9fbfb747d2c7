import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useQuery, useMutation } from '@tanstack/vue-query';
import {
  APIResponseError,
  DailyMission,
  DailyMissionWalk,
  IDailyMissionWalkPayload,
  UserRewards,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/dailymission',
  service: 'HTM',
};

export class DailyMissionModel extends Model {
  static get() {
    return this.api.get<DailyMission[]>({
      url: this.path,
    });
  }

  static claim(unique_id: string) {
    return this.api.post<UserRewards>({
      url: `${this.path}/claim`,
      data: { unique_id },
    });
  }

  static walk(data: IDailyMissionWalkPayload) {
    return this.api.post<DailyMissionWalk>({
      url: `${this.path}/walk`,
      data,
    });
  }

  static shrink() {
    return this.api.post<void>({
      url: `${this.path}/shrink-pu-survey`,
    });
  }
}

DailyMissionModel.setup(modelConfig);

export const DAILY_MISSION_QUERY = 'DAILY_MISSION_QUERY';
export function useDailyMissionQuery(options: Partial<QueryOptions<DailyMission[]>> = {}) {
  return useQuery<DailyMission[], APIResponseError>({
    queryKey: [DAILY_MISSION_QUERY],
    queryFn: () => DailyMissionModel.get().then((r) => r.data),
    ...options,
  });
}

export const DAILY_MISSION_CLAIM_MUTATION = 'DAILY_MISSION_CLAIM_MUTATION';
export function useDailyMissionClaimMutation() {
  return useMutation<UserRewards, APIResponseError, string>({
    mutationKey: [DAILY_MISSION_CLAIM_MUTATION],
    mutationFn: (unique_id: string) => DailyMissionModel.claim(unique_id).then((r) => r.data),
  });
}

export const DAILY_MISSION_WALK_MUTATION = 'DAILY_MISSION_WALK_MUTATION';
export function useDailyMissionWalkMutation() {
  return useMutation<DailyMissionWalk, APIResponseError, IDailyMissionWalkPayload>({
    mutationKey: [DAILY_MISSION_WALK_MUTATION],
    mutationFn: (data: IDailyMissionWalkPayload) =>
      DailyMissionModel.walk(data).then((r) => r.data),
  });
}

export const DAILY_MISSION_SHRINK_MUTATION = 'DAILY_MISSION_SHRINK_MUTATION';
export function useDailyMissionShrinkMutation() {
  return useMutation<void, APIResponseError>({
    mutationKey: [DAILY_MISSION_SHRINK_MUTATION],
    mutationFn: () => DailyMissionModel.shrink().then((r) => r.data),
  });
}
