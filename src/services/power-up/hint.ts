import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useMutation, useQuery } from '@tanstack/vue-query';
import { APIResponseError, BuyHintPayload, DiscountPrice, Hint, HintState } from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/hint',
  service: 'HTM',
};

export class HintModel extends Model {
  static get() {
    return this.api.get<Hint>({
      url: this.path,
    });
  }

  static check() {
    return this.api.get<HintState>({
      url: `${this.path}/check-hint-shop`,
    });
  }

  static buy(data: BuyHintPayload) {
    return this.api.post<Hint>({
      url: `${this.path}/buy`,
      data,
    });
  }

  static price(data: BuyHintPayload) {
    return this.api.post<DiscountPrice>({
      url: `${this.path}/hint-price`,
      data,
    });
  }
}

HintModel.setup(modelConfig);

export const HINT_QUERY = 'HINT_QUERY';
export function useHintQuery(options: Partial<QueryOptions<Hint>> = {}) {
  return useQuery<Hint, APIResponseError>({
    queryKey: [HINT_QUERY],
    queryFn: () => HintModel.get().then((r) => r.data),
    ...options,
  });
}

export const HINT_CHECK_QUERY = 'HINT_CHECK_QUERY';
export function useHintCheckQuery(options: Partial<QueryOptions<HintState>> = {}) {
  return useQuery<HintState, APIResponseError>({
    queryKey: [HINT_CHECK_QUERY],
    queryFn: () => HintModel.check().then((r) => r.data),
    ...options,
  });
}

export const HINT_BUY_MUTATION = 'HINT_BUY_MUTATION';
export function useHintBuyMutation() {
  return useMutation<Hint, APIResponseError, BuyHintPayload>({
    mutationKey: [HINT_BUY_MUTATION],
    mutationFn: (data: BuyHintPayload) => HintModel.buy(data).then((r) => r.data),
  });
}

export const HINT_PRICE_MUTATION = 'HINT_PRICE_MUTATION';
export function useHintPriceMutation() {
  return useMutation<DiscountPrice, APIResponseError, BuyHintPayload>({
    mutationKey: [HINT_PRICE_MUTATION],
    mutationFn: (data: BuyHintPayload) => HintModel.price(data).then((r) => r.data),
  });
}
