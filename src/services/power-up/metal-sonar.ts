import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useMutation, useQuery } from '@tanstack/vue-query';
import {
  APIResponseError,
  MetalSonar,
  MetalSonarPayload,
  MetalSonarPricePayload,
  MetalSonarPricesData,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/silver/metal-sonar',
  service: 'HTM',
};

export class MetalSonarModel extends Model {
  static get() {
    return this.api.get<MetalSonar[]>({
      url: this.path,
    });
  }

  static use(data: MetalSonarPayload) {
    return this.api.post<MetalSonar>({
      url: '/silver/use-metal-sonar',
      data,
    });
  }

  static price(data: MetalSonarPricePayload) {
    return this.api.get<MetalSonarPricesData>({
      url: '/silver/metal-sonar-prices',
      params: data,
    });
  }
}

MetalSonarModel.setup(modelConfig);

export const METAL_SONAR_QUERY = 'METAL_SONAR_QUERY';
export function useMetalSonarQuery(options: Partial<QueryOptions<MetalSonar[]>> = {}) {
  return useQuery<MetalSonar[], APIResponseError>({
    queryKey: [METAL_SONAR_QUERY],
    queryFn: () => MetalSonarModel.get().then((r) => r.data),
    ...options,
  });
}

export const METAL_SONAR_USE_MUTATION = 'METAL_SONAR_USE_MUTATION';
export function useMetalSonarUseMutation() {
  return useMutation<MetalSonar, APIResponseError, MetalSonarPayload>({
    mutationKey: [METAL_SONAR_USE_MUTATION],
    mutationFn: (data) => MetalSonarModel.use(data).then((r) => r.data),
  });
}

export const METAL_SONAR_PRICE_QUERY = 'METAL_SONAR_PRICE_QUERY';
export function useMetalSonarPriceQuery(
  data: MetalSonarPricePayload,
  options: Partial<QueryOptions<MetalSonarPricesData>> = {},
) {
  return useQuery<MetalSonarPricesData, APIResponseError>({
    queryKey: [METAL_SONAR_PRICE_QUERY, data],
    queryFn: () => MetalSonarModel.price(data).then((r) => r.data),
    ...options,
  });
}
