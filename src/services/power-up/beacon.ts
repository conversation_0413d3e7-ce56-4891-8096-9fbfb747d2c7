import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useQuery, useMutation } from '@tanstack/vue-query';
import { APIResponseError, Beacon, BeaconPayload, Beacons } from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/beacon',
  service: 'HTM',
};

export class BeaconModel extends Model {
  static activating() {
    return this.api.get<Beacons>({
      url: this.path,
    });
  }

  static use(data: BeaconPayload) {
    return this.api.post<Beacon>({
      url: `${this.path}/use-beacon`,
      data,
    });
  }
}

BeaconModel.setup(modelConfig);

export const BEACON_ACTIVATING_QUERY = 'BEACON_ACTIVATING_QUERY';
export function useBeaconActivatingQuery(options: Partial<QueryOptions<Beacons>> = {}) {
  return useQuery<Beacons, APIResponseError>({
    queryKey: [BEACON_ACTIVATING_QUERY],
    queryFn: () => BeaconModel.activating().then((r) => r.data),
    ...options,
  });
}

export const BEACON_USE_MUTATION = 'BEACON_USE_MUTATION';
export function useBeaconUseMutation() {
  return useMutation<Beacon, APIResponseError, BeaconPayload>({
    mutationKey: [BEACON_USE_MUTATION],
    mutationFn: (data) => BeaconModel.use(data).then((r) => r.data),
  });
}
