import { ServiceConstructorData, Model } from '@services';
import { useMutation } from '@tanstack/vue-query';
import {
  APIResponseError,
  DiscountPrice,
  MetalDetectorPayload,
  MetalDetectorResult,
  MetalDetectorScanResult,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/silver/metal-detector',
  service: 'HTM',
};

export class MetalDetectorModel extends Model {
  static use(data: MetalDetectorPayload) {
    return this.api.post<MetalDetectorResult>({
      url: this.path,
      data,
    });
  }

  static scan(data: MetalDetectorPayload) {
    return this.api.post<MetalDetectorScanResult>({
      url: `${this.path}/scan`,
      data,
    });
  }

  static extend(item_id: string) {
    return this.api.post<MetalDetectorResult>({
      url: `${this.path}/extend`,
      data: { item_id },
    });
  }

  static price(data: MetalDetectorPayload) {
    return this.api.post<DiscountPrice>({
      url: '/silver/metal-detector-price',
      data,
    });
  }
}

MetalDetectorModel.setup(modelConfig);

export const METAL_DETECTOR_USE_MUTATION = 'METAL_DETECTOR_USE_MUTATION';
export function useMetalDetectorUseMutation() {
  return useMutation<MetalDetectorResult, APIResponseError, MetalDetectorPayload>({
    mutationKey: [METAL_DETECTOR_USE_MUTATION],
    mutationFn: (data: MetalDetectorPayload) => MetalDetectorModel.use(data).then((r) => r.data),
  });
}

export const METAL_DETECTOR_SCAN_MUTATION = 'METAL_DETECTOR_SCAN_MUTATION';
export function useMetalDetectorScanMutation() {
  return useMutation<MetalDetectorScanResult, APIResponseError, MetalDetectorPayload>({
    mutationKey: [METAL_DETECTOR_SCAN_MUTATION],
    mutationFn: (data: MetalDetectorPayload) => MetalDetectorModel.scan(data).then((r) => r.data),
  });
}

export const METAL_DETECTOR_EXTEND_MUTATION = 'METAL_DETECTOR_EXTEND_MUTATION';
export function useMetalDetectorExtendMutation() {
  return useMutation<MetalDetectorResult, APIResponseError, { item_id: string }>({
    mutationKey: [METAL_DETECTOR_EXTEND_MUTATION],
    mutationFn: (data: { item_id: string }) =>
      MetalDetectorModel.extend(data.item_id).then((r) => r.data),
  });
}

export const METAL_DETECTOR_PRICE_MUTATION = 'METAL_DETECTOR_PRICE_MUTATION';
export function useMetalDetectorPriceMutation() {
  return useMutation<DiscountPrice, APIResponseError, MetalDetectorPayload>({
    mutationKey: [METAL_DETECTOR_PRICE_MUTATION],
    mutationFn: (data: MetalDetectorPayload) => MetalDetectorModel.price(data).then((r) => r.data),
  });
}
