import { ServiceConstructorData, Model } from '@services';
import { useMutation } from '@tanstack/vue-query';
import { APIResponseError } from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/user',
  service: 'HTM',
};

export class BonusModel extends Model {
  static daily() {
    return this.api.post<{ crystal: number }>({
      url: `${this.path}/claim-daily-reward`,
    });
  }

  static online() {
    return this.api.post<{ crystal: number }>({
      url: `${this.path}/claim-online-bonus`,
    });
  }
}

BonusModel.setup(modelConfig);

export const BONUS_DAILY_MUTATION = 'BONUS_DAILY_MUTATION';
export function useBonusDailyMutation() {
  return useMutation<{ crystal: number }, APIResponseError>({
    mutationKey: [BONUS_DAILY_MUTATION],
    mutationFn: () => BonusModel.daily().then((r) => r.data),
  });
}

export const BONUS_ONLINE_MUTATION = 'BONUS_ONLINE_MUTATION';
export function useBonusOnlineMutation() {
  return useMutation<{ crystal: number }, APIResponseError>({
    mutationKey: [BONUS_ONLINE_MUTATION],
    mutationFn: () => BonusModel.online().then((r) => r.data),
  });
}
