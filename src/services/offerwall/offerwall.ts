import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useQuery, useMutation } from '@tanstack/vue-query';
import {
  APIResponseError,
  BrandIcon,
  GlobalBrandAction,
  UserRewards,
  PhysicalStorePayload,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/brandaction',
  service: 'HTM',
};

export class OfferWallModel extends Model {
  static get() {
    return this.api.get<GlobalBrandAction>({
      url: this.path,
    });
  }

  static uploadFile(data: FormData) {
    return this.api.postFormData<void>({
      url: `${this.path}/upload-receipt`,
      data,
    });
  }

  static scanQrCode(data: any) {
    return this.api.post<void>({
      url: `${this.path}/scan-qrcode`,
      data,
    });
  }

  static claim(user_brand_action_id: string) {
    return this.api.post<UserRewards>({
      url: `${this.path}/claim`,
      data: {
        user_brand_action_id,
      },
    });
  }

  static claimAll() {
    return this.api.post<UserRewards>({
      url: `${this.path}/claim-all`,
    });
  }

  static visitWeb(brand_action_id: string) {
    return this.api.post<void>({
      url: `${this.path}/visit-web`,
      data: {
        brand_action_id,
      },
    });
  }

  static openSentosaApp(brand_action_id: string) {
    return this.api.post<void>({
      url: `${this.path}/open-sentosa-app`,
      data: {
        brand_action_id,
      },
    });
  }

  static physicalStore(data: PhysicalStorePayload) {
    return this.api.post<void>({
      url: `${this.path}/physical-store`,
      data,
    });
  }

  static notifications(brand_action_id: string) {
    return this.api.get<unknown[]>({
      url: `${this.path}/noti`,
      params: {
        brand_action_id,
      },
    });
  }

  static seenNotification(id: string) {
    return this.api.post<void>({
      url: `${this.path}/seen-noti`,
      data: {
        id,
      },
    });
  }

  static submitClientInfo(data: any) {
    return this.api.post<void>({
      url: `${this.path}/submit-client-info`,
      data,
    });
  }

  static brandIcons() {
    return this.api.get<BrandIcon[]>({
      url: `${this.path}/icons`,
    });
  }
}

OfferWallModel.setup(modelConfig);

export const OFFER_WALL_QUERY = 'OFFER_WALL_QUERY';
export function useOfferWallQuery(options: Partial<QueryOptions<GlobalBrandAction>> = {}) {
  return useQuery<GlobalBrandAction, APIResponseError>({
    queryKey: [OFFER_WALL_QUERY],
    queryFn: () => OfferWallModel.get().then((r) => r.data),
    ...options,
  });
}

export const OFFER_WALL_UPLOAD_FILE_MUTATION = 'OFFER_WALL_UPLOAD_FILE_MUTATION';
export function useOfferWallUploadFileMutation() {
  return useMutation<void, APIResponseError, FormData>({
    mutationKey: [OFFER_WALL_UPLOAD_FILE_MUTATION],
    mutationFn: (data) => OfferWallModel.uploadFile(data).then((r) => r.data),
  });
}

export const OFFER_WALL_SCAN_QR_CODE_MUTATION = 'OFFER_WALL_SCAN_QR_CODE_MUTATION';
export function useOfferWallScanQrCodeMutation() {
  return useMutation<void, APIResponseError, any>({
    mutationKey: [OFFER_WALL_SCAN_QR_CODE_MUTATION],
    mutationFn: (data) => OfferWallModel.scanQrCode(data).then((r) => r.data),
  });
}

export const OFFER_WALL_CLAIM_MUTATION = 'OFFER_WALL_CLAIM_MUTATION';
export function useOfferWallClaimMutation() {
  return useMutation<UserRewards, APIResponseError, string>({
    mutationKey: [OFFER_WALL_CLAIM_MUTATION],
    mutationFn: (user_brand_action_id) =>
      OfferWallModel.claim(user_brand_action_id).then((r) => r.data),
  });
}

export const OFFER_WALL_CLAIM_ALL_MUTATION = 'OFFER_WALL_CLAIM_ALL_MUTATION';
export function useOfferWallClaimAllMutation() {
  return useMutation<UserRewards, APIResponseError, void>({
    mutationKey: [OFFER_WALL_CLAIM_ALL_MUTATION],
    mutationFn: () => OfferWallModel.claimAll().then((r) => r.data),
  });
}

export const OFFER_WALL_VISIT_WEB_MUTATION = 'OFFER_WALL_VISIT_WEB_MUTATION';
export function useOfferWallVisitWebMutation() {
  return useMutation<void, APIResponseError, string>({
    mutationKey: [OFFER_WALL_VISIT_WEB_MUTATION],
    mutationFn: (brand_action_id) => OfferWallModel.visitWeb(brand_action_id).then((r) => r.data),
  });
}

export const OFFER_WALL_OPEN_SENTOSA_APP_MUTATION = 'OFFER_WALL_OPEN_SENTOSA_APP_MUTATION';
export function useOfferWallOpenSentosaAppMutation() {
  return useMutation<void, APIResponseError, string>({
    mutationKey: [OFFER_WALL_OPEN_SENTOSA_APP_MUTATION],
    mutationFn: (brand_action_id) =>
      OfferWallModel.openSentosaApp(brand_action_id).then((r) => r.data),
  });
}

export const OFFER_WALL_PHYSICAL_STORE_MUTATION = 'OFFER_WALL_PHYSICAL_STORE_MUTATION';
export function useOfferWallPhysicalStoreMutation() {
  return useMutation<void, APIResponseError, PhysicalStorePayload>({
    mutationKey: [OFFER_WALL_PHYSICAL_STORE_MUTATION],
    mutationFn: (data) => OfferWallModel.physicalStore(data).then((r) => r.data),
  });
}

export const OFFER_WALL_NOTIFICATIONS_QUERY = 'OFFER_WALL_NOTIFICATIONS_QUERY';
export function useOfferWallNotificationsQuery(
  brand_action_id: string,
  options: Partial<QueryOptions<unknown[]>> = {},
) {
  return useQuery<unknown[], APIResponseError>({
    queryKey: [OFFER_WALL_NOTIFICATIONS_QUERY, brand_action_id],
    queryFn: () => OfferWallModel.notifications(brand_action_id).then((r) => r.data),
    ...options,
  });
}

export const OFFER_WALL_SEEN_NOTIFICATION_MUTATION = 'OFFER_WALL_SEEN_NOTIFICATION_MUTATION';
export function useOfferWallSeenNotificationMutation() {
  return useMutation<void, APIResponseError, string>({
    mutationKey: [OFFER_WALL_SEEN_NOTIFICATION_MUTATION],
    mutationFn: (id) => OfferWallModel.seenNotification(id).then((r) => r.data),
  });
}

export const OFFER_WALL_SUBMIT_CLIENT_INFO_MUTATION = 'OFFER_WALL_SUBMIT_CLIENT_INFO_MUTATION';
export function useOfferWallSubmitClientInfoMutation() {
  return useMutation<void, APIResponseError, any>({
    mutationKey: [OFFER_WALL_SUBMIT_CLIENT_INFO_MUTATION],
    mutationFn: (data) => OfferWallModel.submitClientInfo(data).then((r) => r.data),
  });
}

export const OFFER_WALL_BRAND_ICONS_QUERY = 'OFFER_WALL_BRAND_ICONS_QUERY';
export function useOfferWallBrandIconsQuery(options: Partial<QueryOptions<BrandIcon[]>> = {}) {
  return useQuery<BrandIcon[], APIResponseError>({
    queryKey: [OFFER_WALL_BRAND_ICONS_QUERY],
    queryFn: () => OfferWallModel.brandIcons().then((r) => r.data),
    ...options,
  });
}
