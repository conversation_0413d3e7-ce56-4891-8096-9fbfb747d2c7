import { ServiceConstructorData, Model } from '@services';
import { useMutation } from '@tanstack/vue-query';
import { APIResponseError, LocationBasedPayload, UserRewards, UpdateLocationBased } from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/brandaction/timed-missions',
  service: 'HTM',
};

export class TimedMissionModel extends Model {
  static giveUp() {
    return this.api.post<void>({
      url: `${this.path}/give-up`,
    });
  }

  static claim(user_mission_id: string) {
    return this.api.post<UserRewards>({
      url: `${this.path}/claim-reward`,
      data: {
        user_mission_id,
      },
    });
  }

  static updateLocationBased(data: LocationBasedPayload) {
    return this.api.post<UpdateLocationBased>({
      url: `${this.path}/update-location-based-mission`,
      data,
    });
  }
}

TimedMissionModel.setup(modelConfig);

export const GIVE_UP_TIMED_MISSION_MUTATION = 'GIVE_UP_TIMED_MISSION_MUTATION';
export function useGiveUpTimedMissionMutation() {
  return useMutation<void, APIResponseError>({
    mutationKey: [GIVE_UP_TIMED_MISSION_MUTATION],
    mutationFn: () => TimedMissionModel.giveUp().then((r) => r.data),
  });
}

export const CLAIM_TIMED_MISSION_MUTATION = 'CLAIM_TIMED_MISSION_MUTATION';
export function useClaimTimedMissionMutation() {
  return useMutation<UserRewards, APIResponseError, string>({
    mutationKey: [CLAIM_TIMED_MISSION_MUTATION],
    mutationFn: (user_mission_id) => TimedMissionModel.claim(user_mission_id).then((r) => r.data),
  });
}

export const UPDATE_LOCATION_BASED_TIMED_MISSION_MUTATION =
  'UPDATE_LOCATION_BASED_TIMED_MISSION_MUTATION';
export function useUpdateLocationBasedTimedMissionMutation() {
  return useMutation<UpdateLocationBased, APIResponseError, LocationBasedPayload>({
    mutationKey: [UPDATE_LOCATION_BASED_TIMED_MISSION_MUTATION],
    mutationFn: (data) => TimedMissionModel.updateLocationBased(data).then((r) => r.data),
  });
}
