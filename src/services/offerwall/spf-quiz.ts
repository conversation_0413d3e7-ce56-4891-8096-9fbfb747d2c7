import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useQuery, useMutation } from '@tanstack/vue-query';
import {
  APIResponseError,
  SpfQuizAnswer,
  SpfQuizAnswerPayload,
  SpfQuizData,
  SpfQuizPayload,
  SpfQuizReward,
  SpfQuizShare,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/brandaction/spf-quiz',
  service: 'HTM',
};

export class SpfQuizModel extends Model {
  static get(data: SpfQuizPayload) {
    return this.api.get<SpfQuizData>({
      url: this.path,
      params: data,
    });
  }

  static answer(data: SpfQuizAnswerPayload) {
    return this.api.post<SpfQuizAnswer>({
      url: `${this.path}/answer-quiz`,
      data,
    });
  }

  static share(ba_group_id: string) {
    return this.api.post<SpfQuizShare>({
      url: `${this.path}/share-quiz-result`,
      data: {
        ba_group_id,
      },
    });
  }

  static claim(ba_group_id: string) {
    return this.api.post<SpfQuizReward>({
      url: `${this.path}/claim-reward`,
      data: {
        ba_group_id,
      },
    });
  }
}

SpfQuizModel.setup(modelConfig);

export const SPF_QUIZ_QUERY = 'SPF_QUIZ_QUERY';
export function useSpfQuizQuery(
  data: SpfQuizPayload,
  options: Partial<QueryOptions<SpfQuizData>> = {},
) {
  return useQuery<SpfQuizData, APIResponseError>({
    queryKey: [SPF_QUIZ_QUERY, data],
    queryFn: () => SpfQuizModel.get(data).then((r) => r.data),
    ...options,
  });
}

export const SPF_QUIZ_ANSWER_MUTATION = 'SPF_QUIZ_ANSWER_MUTATION';
export function useSpfQuizAnswerMutation() {
  return useMutation<SpfQuizAnswer, APIResponseError, SpfQuizAnswerPayload>({
    mutationKey: [SPF_QUIZ_ANSWER_MUTATION],
    mutationFn: (data) => SpfQuizModel.answer(data).then((r) => r.data),
  });
}

export const SPF_QUIZ_SHARE_MUTATION = 'SPF_QUIZ_SHARE_MUTATION';
export function useSpfQuizShareMutation() {
  return useMutation<SpfQuizShare, APIResponseError, string>({
    mutationKey: [SPF_QUIZ_SHARE_MUTATION],
    mutationFn: (ba_group_id) => SpfQuizModel.share(ba_group_id).then((r) => r.data),
  });
}

export const SPF_QUIZ_CLAIM_MUTATION = 'SPF_QUIZ_CLAIM_MUTATION';
export function useSpfQuizClaimMutation() {
  return useMutation<SpfQuizReward, APIResponseError, string>({
    mutationKey: [SPF_QUIZ_CLAIM_MUTATION],
    mutationFn: (ba_group_id) => SpfQuizModel.claim(ba_group_id).then((r) => r.data),
  });
}
