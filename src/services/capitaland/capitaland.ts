import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useQuery, useMutation } from '@tanstack/vue-query';
import {
  CapitalandDailyReward,
  CapitalandSpecialCoin,
  APIResponseError,
  AmenitiesCheckInPayload,
  CapitalandAmenity,
  UserRewards,
  BaseLocation,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/capitaland',
  service: 'HTM',
};

export class CapitalandModel extends Model {
  static dailyReward() {
    return this.api.post<CapitalandDailyReward[]>({
      url: `${this.path}/daily-reward`,
    });
  }

  static checkIn(data: BaseLocation) {
    return this.api.post<CapitalandDailyReward>({
      url: `${this.path}/check-in`,
      data,
    });
  }

  static coins() {
    return this.api.get<CapitalandSpecialCoin[]>({
      url: `${this.path}/coins`,
    });
  }

  static amenities() {
    return this.api.get<CapitalandAmenity[]>({
      url: `${this.path}/amenities`,
    });
  }

  static amenitiesCheckIn(data: AmenitiesCheckInPayload) {
    return this.api.post<UserRewards>({
      url: `${this.path}/amenities/check-in`,
      data,
    });
  }
}

CapitalandModel.setup(modelConfig);

export const CAPITALAND_DAILY_REWARD_QUERY = 'CAPITALAND_DAILY_REWARD_QUERY';
export function useCapitalandDailyRewardQuery(
  options: Partial<QueryOptions<CapitalandDailyReward[]>> = {},
) {
  return useQuery<CapitalandDailyReward[], APIResponseError>({
    queryKey: [CAPITALAND_DAILY_REWARD_QUERY],
    queryFn: () => CapitalandModel.dailyReward().then((r) => r.data),
    ...options,
  });
}

export const CAPITALAND_CHECK_IN_MUTATION = 'CAPITALAND_CHECK_IN_MUTATION';
export function useCapitalandCheckInMutation() {
  return useMutation<CapitalandDailyReward, APIResponseError, BaseLocation>({
    mutationKey: [CAPITALAND_CHECK_IN_MUTATION],
    mutationFn: (data) => CapitalandModel.checkIn(data).then((r) => r.data),
  });
}

export const CAPITALAND_COINS_QUERY = 'CAPITALAND_COINS_QUERY';
export function useCapitalandCoinsQuery(
  options: Partial<QueryOptions<CapitalandSpecialCoin[]>> = {},
) {
  return useQuery<CapitalandSpecialCoin[], APIResponseError>({
    queryKey: [CAPITALAND_COINS_QUERY],
    queryFn: () => CapitalandModel.coins().then((r) => r.data),
    ...options,
  });
}

export const CAPITALAND_AMENITIES_QUERY = 'CAPITALAND_AMENITIES_QUERY';
export function useCapitalandAmenitiesQuery(
  options: Partial<QueryOptions<CapitalandAmenity[]>> = {},
) {
  return useQuery<CapitalandAmenity[], APIResponseError>({
    queryKey: [CAPITALAND_AMENITIES_QUERY],
    queryFn: () => CapitalandModel.amenities().then((r) => r.data),
    ...options,
  });
}

export const CAPITALAND_AMENITIES_CHECK_IN_MUTATION = 'CAPITALAND_AMENITIES_CHECK_IN_MUTATION';
export function useCapitalandAmenitiesCheckInMutation() {
  return useMutation<UserRewards, APIResponseError, AmenitiesCheckInPayload>({
    mutationKey: [CAPITALAND_AMENITIES_CHECK_IN_MUTATION],
    mutationFn: (data) => CapitalandModel.amenitiesCheckIn(data).then((r) => r.data),
  });
}
