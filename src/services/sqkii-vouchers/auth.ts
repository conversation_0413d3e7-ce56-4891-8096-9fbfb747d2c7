import { ServiceConstructorData, Model } from '@services';
import { useMutation } from '@tanstack/vue-query';
import {
  APISVResponseError,
  SVActivateOTPPayload,
  SVCheckCredential,
  SVCheckCredentialsPayload,
  SVCreateAccountPayload,
  SVForgotPassword,
  SVLoginPayload,
  SVSetPasswordPayload,
  SVUserLogin,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '',
  service: 'SV',
};

export class AuthSVModel extends Model {
  static login(data: SVLoginPayload) {
    return this.api.post<SVUserLogin>({
      url: '/link-kee',
      data,
    });
  }

  static forgotPassword(email: string) {
    return this.api.post<SVForgotPassword>({
      url: '/forgot-password',
      data: { email },
    });
  }

  static setPassword(data: SVSetPasswordPayload) {
    return this.api.post<void>({
      url: '/set-password',
      data,
    });
  }

  static checkCredential(data: SVCheckCredentialsPayload) {
    return this.api.post<SVCheckCredential>({
      url: '/check-credential',
      data,
    });
  }

  static create(data: SVCreateAccountPayload) {
    return this.api.post<boolean>({
      url: '/create-account',
      data,
    });
  }

  static sendActivate(email: string) {
    return this.api.post<SVForgotPassword>({
      url: '/send-activate',
      data: { email },
    });
  }

  static activateOTP(data: SVActivateOTPPayload) {
    return this.api.post<SVUserLogin>({
      url: '/activate-otp',
      data,
    });
  }
}

AuthSVModel.setup(modelConfig);

export const SV_AUTH_LOGIN_MUTATION = 'SV_AUTH_LOGIN_MUTATION';
export function useSVAuthLoginMutation() {
  return useMutation<SVUserLogin, APISVResponseError, SVLoginPayload>({
    mutationKey: [SV_AUTH_LOGIN_MUTATION],
    mutationFn: (data) => AuthSVModel.login(data).then((r) => r.data),
  });
}

export const SV_AUTH_FORGOT_PASSWORD_MUTATION = 'SV_AUTH_FORGOT_PASSWORD_MUTATION';
export function useSVAuthForgotPasswordMutation() {
  return useMutation<SVForgotPassword, APISVResponseError, string>({
    mutationKey: [SV_AUTH_FORGOT_PASSWORD_MUTATION],
    mutationFn: (email) => AuthSVModel.forgotPassword(email).then((r) => r.data),
  });
}

export const SV_AUTH_SET_PASSWORD_MUTATION = 'SV_AUTH_SET_PASSWORD_MUTATION';
export function useSVAuthSetPasswordMutation() {
  return useMutation<void, APISVResponseError, SVSetPasswordPayload>({
    mutationKey: [SV_AUTH_SET_PASSWORD_MUTATION],
    mutationFn: (data) => AuthSVModel.setPassword(data).then((r) => r.data),
  });
}

export const SV_AUTH_CHECK_CREDENTIAL_MUTATION = 'SV_AUTH_CHECK_CREDENTIAL_MUTATION';
export function useSVAuthCheckCredentialMutation() {
  return useMutation<SVCheckCredential, APISVResponseError, SVCheckCredentialsPayload>({
    mutationKey: [SV_AUTH_CHECK_CREDENTIAL_MUTATION],
    mutationFn: (data) => AuthSVModel.checkCredential(data).then((r) => r.data),
  });
}

export const SV_AUTH_CREATE_MUTATION = 'SV_AUTH_CREATE_MUTATION';
export function useSVAuthCreateMutation() {
  return useMutation<boolean, APISVResponseError, SVCreateAccountPayload>({
    mutationKey: [SV_AUTH_CREATE_MUTATION],
    mutationFn: (data) => AuthSVModel.create(data).then((r) => r.data),
  });
}

export const SV_AUTH_SEND_ACTIVATE_MUTATION = 'SV_AUTH_SEND_ACTIVATE_MUTATION';
export function useSVAuthSendActivateMutation() {
  return useMutation<SVForgotPassword, APISVResponseError, string>({
    mutationKey: [SV_AUTH_SEND_ACTIVATE_MUTATION],
    mutationFn: (email) => AuthSVModel.sendActivate(email).then((r) => r.data),
  });
}

export const SV_AUTH_ACTIVATE_OTP_MUTATION = 'SV_AUTH_ACTIVATE_OTP_MUTATION';
export function useSVAuthActivateOTPMutation() {
  return useMutation<SVUserLogin, APISVResponseError, SVActivateOTPPayload>({
    mutationKey: [SV_AUTH_ACTIVATE_OTP_MUTATION],
    mutationFn: (data) => AuthSVModel.activateOTP(data).then((r) => r.data),
  });
}
