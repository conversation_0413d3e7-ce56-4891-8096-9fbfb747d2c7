import { ServiceConstructorData, Model, QuerySVOptions } from '@services';
import { useMutation, useQuery } from '@tanstack/vue-query';
import {
  APISVResponseError,
  SVChangePinPayload,
  SVForgotPassword,
  SVPayloadChangeMail,
  SVPin,
  SVSetPinOTPPayload,
  SVUserBalance,
  SVUserMe,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '',
  service: 'SV',
};

export class UserSVModel extends Model {
  static me() {
    return this.api.get<SVUserMe>({
      url: '/me',
    });
  }

  static balance() {
    return this.api.get<SVUserBalance>({
      url: '/get-balance',
    });
  }

  static requestChangeMail(mobile_number: string) {
    return this.api.post<SVForgotPassword>({
      url: '/request-change-email',
      data: { mobile_number },
    });
  }

  static changeMail(data: SVPayloadChangeMail) {
    return this.api.put<void>({
      url: '/change-associated-email',
      data,
    });
  }

  static setPin(pin_code: string) {
    return this.api.post<void>({
      url: '/set-pin',
      data: { pin_code },
    });
  }

  static changePin(data: SVChangePinPayload) {
    return this.api.put<void>({
      url: '/change-pin',
      data,
    });
  }

  static forgotPin(mobile_number: string) {
    return this.api.post<SVForgotPassword>({
      url: '/forgot-pin',
      data: { mobile_number },
    });
  }

  static setPinOTP(data: SVSetPinOTPPayload) {
    return this.api.put<SVPin>({
      url: '/set-pin-otp',
      data,
    });
  }

  static verifyPin(pin_code: string) {
    return this.api.post<SVPin>({
      url: '/verify-pin-code',
      data: { pin_code },
    });
  }

  static unlink() {
    return this.api.post<void>({
      url: '/unlink',
      data: {},
    });
  }
}

UserSVModel.setup(modelConfig);

export const SV_USER_QUERY = 'SV_USER_QUERY';
export function useSVUserQuery(options: Partial<QuerySVOptions<SVUserMe>> = {}) {
  return useQuery<SVUserMe, APISVResponseError>({
    queryKey: [SV_USER_QUERY],
    queryFn: () => UserSVModel.me().then((r) => r.data),
    ...options,
  });
}

export const SV_USER_BALANCE_QUERY = 'SV_USER_BALANCE_QUERY';
export function useSVUserBalanceQuery(options: Partial<QuerySVOptions<SVUserBalance>> = {}) {
  return useQuery<SVUserBalance, APISVResponseError>({
    queryKey: [SV_USER_BALANCE_QUERY],
    queryFn: () => UserSVModel.balance().then((r) => r.data),
    ...options,
  });
}

export const SV_USER_REQUEST_CHANGE_MAIL_MUTATION = 'SV_USER_REQUEST_CHANGE_MAIL_MUTATION';
export function useSVAuthRequestChangeMailMutation() {
  return useMutation<SVForgotPassword, APISVResponseError, string>({
    mutationKey: [SV_USER_REQUEST_CHANGE_MAIL_MUTATION],
    mutationFn: (mobile_number) => UserSVModel.requestChangeMail(mobile_number).then((r) => r.data),
  });
}

export const SV_USER_CHANGE_MAIL_MUTATION = 'SV_USER_CHANGE_MAIL_MUTATION';
export function useSVAuthChangeMailMutation() {
  return useMutation<void, APISVResponseError, SVPayloadChangeMail>({
    mutationKey: [SV_USER_CHANGE_MAIL_MUTATION],
    mutationFn: (data) => UserSVModel.changeMail(data).then((r) => r.data),
  });
}

export const SV_USER_SET_PIN_MUTATION = 'SV_USER_SET_PIN_MUTATION';
export function useSVAuthSetPinMutation() {
  return useMutation<void, APISVResponseError, string>({
    mutationKey: [SV_USER_SET_PIN_MUTATION],
    mutationFn: (pin_code) => UserSVModel.setPin(pin_code).then((r) => r.data),
  });
}

export const SV_USER_CHANGE_PIN_MUTATION = 'SV_USER_CHANGE_PIN_MUTATION';
export function useSVAuthChangePinMutation() {
  return useMutation<void, APISVResponseError, SVChangePinPayload>({
    mutationKey: [SV_USER_CHANGE_PIN_MUTATION],
    mutationFn: (data) => UserSVModel.changePin(data).then((r) => r.data),
  });
}

export const SV_USER_FORGOT_PIN_MUTATION = 'SV_USER_FORGOT_PIN_MUTATION';
export function useSVAuthForgotPinMutation() {
  return useMutation<SVForgotPassword, APISVResponseError, string>({
    mutationKey: [SV_USER_FORGOT_PIN_MUTATION],
    mutationFn: (mobile_number) => UserSVModel.forgotPin(mobile_number).then((r) => r.data),
  });
}

export const SV_USER_SET_PIN_OTP_MUTATION = 'SV_USER_SET_PIN_OTP_MUTATION';
export function useSVAuthSetPinOTPMutation() {
  return useMutation<SVPin, APISVResponseError, SVSetPinOTPPayload>({
    mutationKey: [SV_USER_SET_PIN_OTP_MUTATION],
    mutationFn: (data) => UserSVModel.setPinOTP(data).then((r) => r.data),
  });
}

export const SV_USER_VERIFY_PIN_MUTATION = 'SV_USER_VERIFY_PIN_MUTATION';
export function useSVVerifyPinMutation() {
  return useMutation<SVPin, APISVResponseError, string>({
    mutationKey: [SV_USER_VERIFY_PIN_MUTATION],
    mutationFn: (pin_code) => UserSVModel.verifyPin(pin_code).then((r) => r.data),
  });
}

export const SV_USER_UNLINK_MUTATION = 'SV_USER_UNLINK_MUTATION';
export function useSVAuthUnlinkMutation() {
  return useMutation<void, APISVResponseError>({
    mutationKey: [SV_USER_UNLINK_MUTATION],
    mutationFn: () => UserSVModel.unlink().then((r) => r.data),
  });
}
