import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useMutation, useQuery } from '@tanstack/vue-query';
import { APIResponseError, ReminderPayload, TimelineLogs } from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/timeline',
  service: 'HTM',
};

export class TimelineModel extends Model {
  static get() {
    return this.api.get<TimelineLogs[]>({
      url: this.path,
    });
  }

  static reminder(data: ReminderPayload) {
    return this.api.post<void>({
      url: '/season-consent',
      data,
    });
  }
}

TimelineModel.setup(modelConfig);

export const TIMELINE_QUERY = 'TIMELINE_QUERY';
export function useTimelineQuery(options: Partial<QueryOptions<TimelineLogs[]>> = {}) {
  return useQuery<TimelineLogs[], APIResponseError>({
    queryKey: [TIMELINE_QUERY],
    queryFn: () => TimelineModel.get().then((r) => r.data),
    ...options,
  });
}

export const TIMELINE_REMINDER_MUTATION = 'TIMELINE_REMINDER_MUTATION';
export function useTimelineReminderMutation() {
  return useMutation<void, APIResponseError, ReminderPayload>({
    mutationKey: [TIMELINE_REMINDER_MUTATION],
    mutationFn: (data) => TimelineModel.reminder(data).then((r) => r.data),
  });
}
