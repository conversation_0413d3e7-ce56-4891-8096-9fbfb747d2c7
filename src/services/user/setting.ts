import { useMutation, useQuery } from '@tanstack/vue-query';
import { ServiceConstructorData, Model, QueryOptions } from '@services';
import {
  AdventureLogs,
  APIResponseError,
  BrandSOVSettings,
  Settings,
  TrackingPayload,
} from '@types';
import axios from 'axios';

const modelConfig: ServiceConstructorData = {
  path: '',
  service: 'HTM',
};

export class SettingModel extends Model {
  static setting() {
    return this.api.get<Settings>({
      url: '/setting',
    });
  }

  static brandSOVSetting() {
    return this.api.get<BrandSOVSettings>({
      url: '/brand-sov-setting',
    });
  }

  static adventureLogs() {
    return this.api.get<AdventureLogs>({
      url: '/adventure-logs',
    });
  }

  static trackEvent(data: TrackingPayload) {
    return this.api.post<void>({
      url: '/track',
      data,
    });
  }

  static countryCode() {
    return axios.get<{ country: string }>('https://api.country.is');
  }
}

SettingModel.setup(modelConfig);

export const USER_SETTING_QUERY = 'USER_SETTING_QUERY';
export function useSettingQuery(options: Partial<QueryOptions<Settings>> = {}) {
  return useQuery<Settings, APIResponseError>({
    queryKey: [USER_SETTING_QUERY],
    queryFn: () => SettingModel.setting().then((r) => r.data),
    ...options,
  });
}

export const BRAND_SOV_SETTING_QUERY = 'BRAND_SOV_SETTING_QUERY';
export function useBrandSOVSettingQuery(options: Partial<QueryOptions<BrandSOVSettings>> = {}) {
  return useQuery<BrandSOVSettings, APIResponseError>({
    queryKey: [BRAND_SOV_SETTING_QUERY],
    queryFn: () => SettingModel.brandSOVSetting().then((r) => r.data),
    ...options,
  });
}

export const ADVENTURE_LOGS_QUERY = 'ADVENTURE_LOGS_QUERY';
export function useAdventureLogsQuery(options: Partial<QueryOptions<AdventureLogs>> = {}) {
  return useQuery<AdventureLogs, APIResponseError>({
    queryKey: [ADVENTURE_LOGS_QUERY],
    queryFn: () => SettingModel.adventureLogs().then((r) => r.data),
    ...options,
  });
}

export const TRACK_EVENT_MUTATION = 'TRACK_EVENT_MUTATION';
export function useTrackEventMutation() {
  return useMutation<void, APIResponseError, TrackingPayload>({
    mutationKey: [TRACK_EVENT_MUTATION],
    mutationFn: (data) => SettingModel.trackEvent(data).then((r) => r.data),
  });
}

export const COUNTRY_CODE_QUERY = 'COUNTRY_CODE_QUERY';
export function useCountryCodeQuery(options: Partial<QueryOptions<{ country: string }>> = {}) {
  return useQuery<{ country: string }, APIResponseError>({
    queryKey: [COUNTRY_CODE_QUERY],
    queryFn: () => SettingModel.countryCode().then((r) => r.data),
    ...options,
  });
}
