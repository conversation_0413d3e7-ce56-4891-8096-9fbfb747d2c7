import { ServiceConstructorData, Model, QueryOptions } from '@services';
import { useMutation, useQuery } from '@tanstack/vue-query';
import { APIResponseError, UserReferral } from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/user',
  service: 'HTM',
};

export class ReferralModel extends Model {
  static get() {
    return this.api.get<UserReferral[]>({
      url: `${this.path}/referrals`,
    });
  }

  static code(referral_code: string) {
    return this.api.post<void>({
      url: `${this.path}/enter-referral-code`,
      data: { referral_code },
    });
  }

  static claim(id: string) {
    return this.api.post<void>({
      url: `${this.path}/claim-referral`,
      data: { id },
    });
  }
}

ReferralModel.setup(modelConfig);

export const REFERRAL_QUERY = 'REFERRAL_QUERY';
export function useReferralQuery(options: Partial<QueryOptions<UserReferral[]>> = {}) {
  return useQuery<UserReferral[], APIResponseError>({
    queryKey: [REFERRAL_QUERY],
    queryFn: () => ReferralModel.get().then((r) => r.data),
    ...options,
  });
}

export const REFERRAL_CODE_MUTATION = 'REFERRAL_CODE_MUTATION';
export function useReferralCodeMutation() {
  return useMutation<void, APIResponseError, string>({
    mutationKey: [REFERRAL_CODE_MUTATION],
    mutationFn: (referral_code) => ReferralModel.code(referral_code).then((r) => r.data),
  });
}

export const REFERRAL_CLAIM_MUTATION = 'REFERRAL_CLAIM_MUTATION';
export function useReferralClaimMutation() {
  return useMutation<void, APIResponseError, string>({
    mutationKey: [REFERRAL_CLAIM_MUTATION],
    mutationFn: (id) => ReferralModel.claim(id).then((r) => r.data),
  });
}
