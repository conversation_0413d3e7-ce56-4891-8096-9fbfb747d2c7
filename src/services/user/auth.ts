import { ServiceConstructorData, Model } from '@services';
import { useMutation } from '@tanstack/vue-query';
import {
  APIResponseError,
  GuestUser,
  LoginPayload,
  ResendOTP,
  ResendOTPPayload,
  SignUpPayload,
  User,
  VerifyMobileNumberPayload,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/user',
  service: 'HTM',
};

export class AuthModel extends Model {
  static login(data: LoginPayload) {
    return this.api.post<GuestUser>({
      url: '/login',
      data,
    });
  }

  static signUp(data: Partial<SignUpPayload>) {
    return this.api.post<User>({
      url: `${this.path}/register`,
      data,
    });
  }

  static resendOTP(data: ResendOTPPayload) {
    return this.api.post<ResendOTP>({
      url: `${this.path}/resend-otp`,
      data,
    });
  }

  static forgotPassword(data: Partial<SignUpPayload>) {
    return this.api.post<User>({
      url: '/forgot-password',
      data,
    });
  }

  static verifyMobileNumber(data: VerifyMobileNumberPayload) {
    return this.api.post<User>({
      url: `${this.path}/verify-mobile-number`,
      data,
    });
  }
}

AuthModel.setup(modelConfig);

export const USER_LOGIN_MUTATION = 'USER_LOGIN_MUTATION';
export function useUserLoginMutation() {
  return useMutation<GuestUser, APIResponseError, LoginPayload>({
    mutationKey: [USER_LOGIN_MUTATION],
    mutationFn: (data) => AuthModel.login(data).then((r) => r.data),
  });
}

export const USER_SIGNUP_MUTATION = 'USER_SIGNUP_MUTATION';
export function useUserSignUpMutation() {
  return useMutation<User, APIResponseError, Partial<SignUpPayload>>({
    mutationKey: [USER_SIGNUP_MUTATION],
    mutationFn: (data) => AuthModel.signUp(data).then((r) => r.data),
  });
}

export const USER_RESEND_OTP_MUTATION = 'USER_RESEND_OTP_MUTATION';
export function useUserResendOTPMutation() {
  return useMutation<ResendOTP, APIResponseError, ResendOTPPayload>({
    mutationKey: [USER_RESEND_OTP_MUTATION],
    mutationFn: (data) => AuthModel.resendOTP(data).then((r) => r.data),
  });
}

export const USER_FORGOT_PASSWORD_MUTATION = 'USER_FORGOT_PASSWORD_MUTATION';
export function useUserForgotPasswordMutation() {
  return useMutation<User, APIResponseError, Partial<SignUpPayload>>({
    mutationKey: [USER_FORGOT_PASSWORD_MUTATION],
    mutationFn: (data) => AuthModel.forgotPassword(data).then((r) => r.data),
  });
}

export const USER_VERIFY_MOBILE_NUMBER_MUTATION = 'USER_VERIFY_MOBILE_NUMBER_MUTATION';
export function useUserVerifyMobileNumberMutation() {
  return useMutation<User, APIResponseError, VerifyMobileNumberPayload>({
    mutationKey: [USER_VERIFY_MOBILE_NUMBER_MUTATION],
    mutationFn: (data) => AuthModel.verifyMobileNumber(data).then((r) => r.data),
  });
}
