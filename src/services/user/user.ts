import { useMutation, useQuery } from '@tanstack/vue-query';
import { ServiceConstructorData, Model, QueryOptions } from '@services';
import {
  APIResponseError,
  ChangeHunterIdFee,
  CrystalExpiring,
  GuestUser,
  Notification,
  TrackUserBaseLocation,
  PedometerDailyProgress,
  ReadAnnouncementPayload,
  Transaction,
  User,
  UserSettingPayload,
  TOnboarding,
  TUserLang,
  TigerBrokerTokenPayload,
  SqkiiVoucherReferral,
  SqkiiVoucherReferralPayload,
  UserRewards,
  AppLocalization,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '/user',
  service: 'HTM',
};

export class UserModel extends Model {
  static localize() {
    return this.api.get<AppLocalization[]>({
      url: '/content/all',
    });
  }

  static guest() {
    return this.api.get<GuestUser>({
      url: '/guest',
    });
  }

  static me() {
    return this.api.get<User>({
      url: `${this.path}/me`,
    });
  }

  static logout() {
    return this.api.post<void>({
      url: `${this.path}/logout`,
    });
  }

  static onboarding(type: TOnboarding) {
    return this.api.post<void>({
      url: `${this.path}/onboarding`,
      data: {
        type,
      },
    });
  }

  static updateUserSetting(data: UserSettingPayload) {
    return this.api.post<void>({
      url: `${this.path}/setting`,
      data,
    });
  }

  static changeLanguage(language: TUserLang) {
    return this.api.put<void>({
      url: this.path,
      data: {
        lang: language,
      },
    });
  }

  static changeHunterId() {
    return this.api.post<ChangeHunterIdFee>({
      url: `${this.path}/change-hunter-id`,
    });
  }

  static updateDroppedCoin(ids: string[]) {
    return this.api.post<void>({
      url: `${this.path}/coin-drop`,
      data: {
        ids,
      },
    });
  }

  static updateMidSurvey(data: any) {
    return this.api.post<void>({
      url: `${this.path}/survey`,
      data,
    });
  }

  static claimMidSurveyReward() {
    return this.api.get<UserRewards>({
      url: `${this.path}/survey-reward`,
    });
  }

  static updateEndSurvey(data: any) {
    return this.api.post<void>({
      url: `${this.path}/end-survey`,
      data,
    });
  }

  static claimEndSurveyReward() {
    return this.api.get<UserRewards>({
      url: `${this.path}/end-survey-reward`,
    });
  }

  static announcement() {
    return this.api.get<Notification[]>({
      url: `${this.path}/announcements`,
    });
  }

  static readAnnouncement(data: ReadAnnouncementPayload) {
    return this.api.put<void>({
      url: `${this.path}/announcements/${data.id}`,
      data: {
        skip: data.skip || false,
      },
    });
  }

  static updateLocation(data: TrackUserBaseLocation) {
    return this.api.post<void>({
      url: `${this.path}/location`,
      data,
    });
  }

  static pedometerProgress() {
    return this.api.get<PedometerDailyProgress[]>({
      url: `${this.path}/pedometer`,
    });
  }

  static transactions() {
    return this.api.get<Transaction[]>({
      url: `${this.path}/transactions`,
    });
  }

  static crystalExpiring() {
    return this.api.get<CrystalExpiring>({
      url: `${this.path}/crystal-expiring`,
    });
  }

  static tigerBrokerToken(data: TigerBrokerTokenPayload) {
    return this.api.post<void>({
      url: `${this.path}/tiger-broker-token`,
      data,
    });
  }

  static updateSVToken(token: string) {
    return this.api.post<void>({
      url: `${this.path}/set-sv-token`,
      data: {
        token,
      },
    });
  }

  static svReferral() {
    return this.api.get<SqkiiVoucherReferral>({
      url: `${this.path}/sv-referral`,
    });
  }

  static submitSVReferral(data: SqkiiVoucherReferralPayload) {
    return this.api.post<boolean>({
      url: `${this.path}/sv-referral`,
      data,
    });
  }
}

UserModel.setup(modelConfig);

export const USER_GUEST_QUERY = 'USER_GUEST_QUERY';
export function useGuestQuery(options: Partial<QueryOptions<GuestUser>> = {}) {
  return useQuery<GuestUser, APIResponseError>({
    queryKey: [USER_GUEST_QUERY],
    queryFn: () => UserModel.guest().then((r) => r.data),
    ...options,
  });
}

export const USER_ME_QUERY = 'USER_ME_QUERY';
export function useGetMeQuery(options: Partial<QueryOptions<User>> = {}) {
  return useQuery<User, APIResponseError>({
    queryKey: [USER_ME_QUERY],
    queryFn: () => UserModel.me().then((r) => r.data),
    ...options,
  });
}

export const USER_LOGOUT_MUTATION = 'USER_LOGOUT_MUTATION';
export function useLogoutMutation() {
  return useMutation<void, APIResponseError>({
    mutationKey: [USER_LOGOUT_MUTATION],
    mutationFn: () => UserModel.logout().then((r) => r.data),
  });
}

export const USER_ONBOARDING_MUTATION = 'USER_ONBOARDING_MUTATION';
export function useOnboardingMutation() {
  return useMutation<void, APIResponseError, { type: TOnboarding }>({
    mutationKey: [USER_ONBOARDING_MUTATION],
    mutationFn: ({ type }) => UserModel.onboarding(type).then((r) => r.data),
  });
}

export const USER_UPDATE_SETTING_MUTATION = 'USER_UPDATE_SETTING_MUTATION';
export function useUpdateUserSettingMutation() {
  return useMutation<void, APIResponseError, UserSettingPayload>({
    mutationKey: [USER_UPDATE_SETTING_MUTATION],
    mutationFn: (data) => UserModel.updateUserSetting(data).then((r) => r.data),
  });
}

export const USER_CHANGE_LANGUAGE_MUTATION = 'USER_CHANGE_LANGUAGE_MUTATION';
export function useChangeLanguageMutation() {
  return useMutation<void, APIResponseError, TUserLang>({
    mutationKey: [USER_CHANGE_LANGUAGE_MUTATION],
    mutationFn: (language) => UserModel.changeLanguage(language).then((r) => r.data),
  });
}

export const USER_CHANGE_HUNTER_ID_MUTATION = 'USER_CHANGE_HUNTER_ID_MUTATION';
export function useChangeHunterIdMutation() {
  return useMutation<ChangeHunterIdFee, APIResponseError>({
    mutationKey: [USER_CHANGE_HUNTER_ID_MUTATION],
    mutationFn: () => UserModel.changeHunterId().then((r) => r.data),
  });
}

export const USER_UPDATE_DROPPED_COIN_MUTATION = 'USER_UPDATE_DROPPED_COIN_MUTATION';
export function useUpdateDroppedCoinMutation() {
  return useMutation<void, APIResponseError, string[]>({
    mutationKey: [USER_UPDATE_DROPPED_COIN_MUTATION],
    mutationFn: (ids) => UserModel.updateDroppedCoin(ids).then((r) => r.data),
  });
}

export const USER_UPDATE_SURVEY_MUTATION = 'USER_UPDATE_SURVEY_MUTATION';
export function useUpdateMidSurveyMutation() {
  return useMutation<void, APIResponseError, any>({
    mutationKey: [USER_UPDATE_SURVEY_MUTATION],
    mutationFn: (data) => UserModel.updateMidSurvey(data).then((r) => r.data),
  });
}

export const USER_CLAIM_SURVEY_REWARD_MUTATION = 'USER_CLAIM_SURVEY_REWARD_MUTATION';
export function useClaimMidSurveyRewardMutation() {
  return useMutation<UserRewards, APIResponseError>({
    mutationKey: [USER_CLAIM_SURVEY_REWARD_MUTATION],
    mutationFn: () => UserModel.claimMidSurveyReward().then((r) => r.data),
  });
}

export const USER_UPDATE_END_SURVEY_MUTATION = 'USER_UPDATE_END_SURVEY_MUTATION';
export function useUpdateEndSurveyMutation() {
  return useMutation<void, APIResponseError, any>({
    mutationKey: [USER_UPDATE_END_SURVEY_MUTATION],
    mutationFn: (data) => UserModel.updateEndSurvey(data).then((r) => r.data),
  });
}

export const USER_CLAIM_END_SURVEY_REWARD_MUTATION = 'USER_CLAIM_END_SURVEY_REWARD_MUTATION';
export function useClaimEndSurveyRewardMutation() {
  return useMutation<UserRewards, APIResponseError>({
    mutationKey: [USER_CLAIM_END_SURVEY_REWARD_MUTATION],
    mutationFn: () => UserModel.claimEndSurveyReward().then((r) => r.data),
  });
}

export const USER_ANNOUNCEMENT_QUERY = 'USER_ANNOUNCEMENT_QUERY';
export function useAnnouncementQuery(options: Partial<QueryOptions<Notification[]>> = {}) {
  return useQuery<Notification[], APIResponseError>({
    queryKey: [USER_ANNOUNCEMENT_QUERY],
    queryFn: () => UserModel.announcement().then((r) => r.data),
    ...options,
  });
}

export const USER_READ_ANNOUNCEMENT_MUTATION = 'USER_READ_ANNOUNCEMENT_MUTATION';
export function useReadAnnouncementMutation() {
  return useMutation<void, APIResponseError, ReadAnnouncementPayload>({
    mutationKey: [USER_READ_ANNOUNCEMENT_MUTATION],
    mutationFn: (data) => UserModel.readAnnouncement(data).then((r) => r.data),
  });
}

export const USER_UPDATE_LOCATION_MUTATION = 'USER_UPDATE_LOCATION_MUTATION';
export function useUpdateLocationMutation() {
  return useMutation<void, APIResponseError, TrackUserBaseLocation>({
    mutationKey: [USER_UPDATE_LOCATION_MUTATION],
    mutationFn: (data) => UserModel.updateLocation(data).then((r) => r.data),
  });
}

export const USER_PEDOMETER_PROGRESS_QUERY = 'USER_PEDOMETER_PROGRESS_QUERY';
export function usePedometerProgressQuery(
  options: Partial<QueryOptions<PedometerDailyProgress[]>> = {},
) {
  return useQuery<PedometerDailyProgress[], APIResponseError>({
    queryKey: [USER_PEDOMETER_PROGRESS_QUERY],
    queryFn: () => UserModel.pedometerProgress().then((r) => r.data),
    ...options,
  });
}

export const USER_TRANSACTIONS_QUERY = 'USER_TRANSACTIONS_QUERY';
export function useTransactionsQuery(options: Partial<QueryOptions<Transaction[]>> = {}) {
  return useQuery<Transaction[], APIResponseError>({
    queryKey: [USER_TRANSACTIONS_QUERY],
    queryFn: () => UserModel.transactions().then((r) => r.data),
    ...options,
  });
}

export const USER_CRYSTAL_EXPIRING_QUERY = 'USER_CRYSTAL_EXPIRING_QUERY';
export function useCrystalExpiringQuery(options: Partial<QueryOptions<CrystalExpiring>> = {}) {
  return useQuery<CrystalExpiring, APIResponseError>({
    queryKey: [USER_CRYSTAL_EXPIRING_QUERY],
    queryFn: () => UserModel.crystalExpiring().then((r) => r.data),
    ...options,
  });
}

export const USER_TIGER_BROKER_TOKEN_MUTATION = 'USER_TIGER_BROKER_TOKEN_MUTATION';
export function useTigerBrokerTokenMutation() {
  return useMutation<void, APIResponseError, TigerBrokerTokenPayload>({
    mutationKey: [USER_TIGER_BROKER_TOKEN_MUTATION],
    mutationFn: (data) => UserModel.tigerBrokerToken(data).then((r) => r.data),
  });
}

export const USER_UPDATE_SQKII_SV_TOKEN_MUTATION = 'USER_UPDATE_SQKII_SV_TOKEN_MUTATION';
export function useUpdateSVTokenMutation() {
  return useMutation<void, APIResponseError, string>({
    mutationKey: [USER_UPDATE_SQKII_SV_TOKEN_MUTATION],
    mutationFn: (token) => UserModel.updateSVToken(token).then((r) => r.data),
  });
}

export const USER_SQKII_VOUCHER_REFERRAL_QUERY = 'USER_SQKII_VOUCHER_REFERRAL_QUERY';
export function useSVReferralQuery(options: Partial<QueryOptions<SqkiiVoucherReferral>> = {}) {
  return useQuery<SqkiiVoucherReferral, APIResponseError>({
    queryKey: [USER_SQKII_VOUCHER_REFERRAL_QUERY],
    queryFn: () => UserModel.svReferral().then((r) => r.data),
    ...options,
  });
}

export const USER_SUBMIT_SQKII_VOUCHER_REFERRAL_MUTATION =
  'USER_SUBMIT_SQKII_VOUCHER_REFERRAL_MUTATION';
export function useSubmitSVReferralMutation() {
  return useMutation<boolean, APIResponseError, SqkiiVoucherReferralPayload>({
    mutationKey: [USER_SUBMIT_SQKII_VOUCHER_REFERRAL_MUTATION],
    mutationFn: (data) => UserModel.submitSVReferral(data).then((r) => r.data),
  });
}
