<script lang="ts" setup>
import { Loading } from '@components';
import { useFetchQueries } from '@composables';
import { usePageTracker } from '@composables';

const { fetched } = useFetchQueries(true);
const { setupGlobalLinkTracking } = usePageTracker();

const isMobile = computed(() => {
  const mobileRegex =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|Tablet|Kindle|Silk|Windows Phone|PlayBook|BB10|Nexus|Surface|Lumia|Samsung|Galaxy|SM-|SC-|GT-|Nexus|Pixel|Mi |Redmi|Fire|TouchPad/i;

  const isUserAgentMobile = mobileRegex.test(navigator.userAgent);

  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  return isUserAgentMobile || isTouchDevice || window.innerWidth <= 768;
});

const loading = ref(true);

onMounted(async () => {
  await nextTick();
  setupGlobalLinkTracking();
});
</script>

<template>
  <template v-if="isMobile">
    <q-dialog
      v-model="loading"
      fullscreen
      position="standard"
      transition-show="none"
      transition-hide="fade"
    >
      <Loading :fetched="fetched" @success="loading = false" />
    </q-dialog>
  </template>

  <RouterView />
</template>
