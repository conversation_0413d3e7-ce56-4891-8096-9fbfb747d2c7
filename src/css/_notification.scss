.q-notification {
  box-shadow: none;
  width: 100%;
  padding: 5% 0;
  min-height: 85px;
  .q-notification__content {
    padding: 20px 30px;
    .q-notification__message {
      padding: unset;
      font-size: 14px;
      line-height: 16px;
    }
  }
  .q-notification__wrapper {
    .q-notification__actions {
      position: absolute;
      top: 12px;
      right: 18px;
      width: 16px;
      height: 16px;
      padding: unset;
      background-image: url('/imgs/cross.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
}

.notify-success {
  background: url('/imgs/notify-success.png');
  background-size: 100% 100%;
}

.notify-error {
  background: url('/imgs/notify-error.png');
  background-size: 100% 100%;
}
.errors-notify {
  @extend .notify-error;
}
