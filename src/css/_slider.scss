.q-slider {
  .q-slider__track {
    height: 12px !important;
    border-radius: 12px !important;
    background: #42464d;
  }
  .q-slider__selection {
    background: linear-gradient(90deg, #7121ff -0.11%, #13b7ad 149.68%);
  }
  .q-slider__thumb {
    width: 30px !important;
    height: 30px !important;
    border-radius: 50%;
    border: 4px solid #182744;
    position: relative;
    background: linear-gradient(90deg, #7121ff -0.11%, #13b7ad 149.68%);

    &::after {
      content: '';
      position: absolute;
      inset: 4px;
      background-color: #182744;
      border-radius: 50%;
    }
    .q-slider__thumb-shape {
      display: none;
    }
    svg {
      opacity: 0;
    }
    .q-slider__text-container {
      padding: 0 !important;
    }
    .q-slider__text {
      text-align: center;
      font-family: Karl<PERSON>;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: 18px;
      margin-left: -40px;
    }
  }
  .q-slider__markers {
    height: 16px;
    top: -2px;
    border-radius: 0px !important;
    color: #12caf3 !important;
    &::after {
      border-radius: 2px !important;
    }
  }
  .q-slider__track-container--h {
    padding: 6px 0;
  }
  // .q-slider__text-container {
  //   padding: 0 !important;
  // }
  .q-slider__pin {
    background: url(/imgs/star/active.png);
    position: absolute;
    top: -30px;
    left: 10px;
    display: flex;
    align-items: center;
    z-index: 9999;
    width: 20px !important;
    height: 20px !important;

    background-size: 100% 100%;
    .q-slider__label {
      background: transparent;
      .q-slider__text-container {
        background: transparent;
        margin-bottom: -15px;
      }
    }
    &::before {
      display: none;
    }
  }
}
.hide-pin {
  .q-slider__pin {
    background: transparent !important;
    top: -25px !important;
    left: 10px !important;
    .q-slider__text {
      margin-left: -10px !important;
    }
  }
}
