.qselect-box {
  .q-select {
    height: 44px !important;
  }
  .q-field--outlined.q-field--highlighted .q-field__control::after {
    border: none !important;
  }
  .q-field--focused {
    background: none !important;
  }

  &.primary {
    .q-field__label {
      color: #505050;
      font-size: 12px;
      line-height: 14px;
      top: 15px;
      overflow: visible;
    }
    .q-field--dense.q-field--float .q-field__label {
      top: 10px !important;
    }
  }

  .q-field__label {
    color: rgb(255, 255, 255, 0.5);
    padding: 0 12px !important;
  }

  .q-field__control::before,
  .q-field__control::after {
    border: none !important;
  }
  &.secondary {
    .q-field__control {
      background: #2e3b54;
      border-radius: 4px;
      border: solid 1px #364c76;
      width: 100%;
      height: 44px !important;
      min-height: unset !important;
    }
  }
  &.neon-outline {
    .q-field__control {
      border-radius: 10px;
      border: 1px solid #00e0ff;
      background: #04081d;
      width: 100%;
      height: 44px !important;
      min-height: unset !important;
    }
  }
  &.primary {
    .q-field__control {
      background: #04081d;
      border-radius: 10px;
      border: solid 1px #04081d;
      width: 100%;
      height: 44px !important;
      min-height: unset !important;
    }
  }
  &.error-select {
    .q-field__label {
      color: white;
    }
    .q-field__control {
      border: 1px solid #ff0000 !important;
      box-shadow: 2px 2px 10px rgba(255, 32, 32, 0.4);
    }
  }
  .q-field__control-container {
    padding-left: 12px;
  }
  .q-field__native {
    position: absolute;
    top: 50%;
    transform: translateY(-40%);
    left: 8px;
    color: white;
    padding: 0 !important;
    height: 42px;
    min-height: 30px !important;
  }
  .selected-country .normal-flag {
    margin: 0 !important;
  }
  .q-field__append .q-icon {
    right: 0;
    color: rgb(255, 255, 255, 0.5);
  }
}

.bg-opt-secondary {
  background: #2e3b54 !important;
}

.bg-opt-primary {
  background: #04081d !important;
}
.bg-opt-neon-outline {
  background: #04081d !important;
}

.qselect-country {
  .q-field--outlined.q-field--highlighted .q-field__control::after {
    border: none !important;
  }
  .q-field--focused {
    background: none !important;
  }
  .q-field__control::before,
  .q-field__control::after {
    border: none !important;
  }
  .q-field__control {
    background: rgba(0, 0, 0, 0);
    border-radius: 8px;
    width: 60x;
    margin-left: 0px;
    height: 30px !important;
    min-height: 30px !important;
  }

  .q-field__native {
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    padding: 0 !important;
    min-height: 30px !important;
  }
  .selected-country .normal-flag {
    margin: 0 !important;
  }
  .q-field__append {
    padding-left: 0;
  }
  .q-field__append .q-icon {
    right: 0;
  }
  .flag {
    top: 7px;
    left: 7px;
    position: absolute;
  }
  &.hide-arrow {
    .q-field__marginal {
      display: none;
    }
  }
}

.popup {
  max-height: 200px !important;
  width: calc(100% - 108px) !important;
  // max-width: 280px;
  background: linear-gradient(to bottom, #001432, #18335b);
  color: #ffffff;
  margin: 5px 0 !important;
  left: 50% !important;
  transform: translateX(-50%);
  .q-item__section {
    .q-img--menu {
      margin-top: 8px;
    }
  }
}

.text-deep {
  color: #00f7ff !important;
  background-color: rgba($color: #00f7ff, $alpha: 0.5);
}
