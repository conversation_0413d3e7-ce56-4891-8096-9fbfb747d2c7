.onesignal-bell-container {
  display: none !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.maplibregl-ctrl-bottom-right {
  display: none;
}

.maplibregl-marker {
  width: 32px;
  height: 47px;
  background-size: cover;
  background-repeat: no-repeat;
  svg {
    display: none;
  }
}

.maplibregl-marker {
  &.grid-marker {
    width: 32px;
    height: 47px;
    background-image: url(/imgs/map/not-there.png);
    background-size: cover;
    background-repeat: no-repeat;
    svg {
      display: none;
    }
  }
}
.maplibregl-user-location-accuracy-circle {
  display: none;
}

.maplibregl-user-location-dot {
  background-image: unset !important;
}

.verifying-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    min-width: 150px;
    background-image: url('/imgs/map/verifying-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.forfeited-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    min-width: 200px;
    background-image: url('/imgs/map/forfeited-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.paid-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    min-width: 85px;
    background-image: url('/imgs/map/paid-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.timeup-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    min-width: 150px;
    background-image: url('/imgs/map/time-up-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.found-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    min-width: 180px;
    background-image: url('/imgs/map/winner-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.beacon-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    min-width: 150px;
    background-image: url('/imgs/map/winner-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.golden-found-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    min-width: 180px;
    background-image: url('/imgs/map/winner-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.metal-sonar-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    max-width: 220px;
    background-image: url('/imgs/map/metal-sonar-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.user-location-popup {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    font-size: 14px;
    padding: 4px 8px;
    text-align: center;
    border-radius: 4px;
    min-width: 150px;
    background-image: url('/imgs/map/free-popup.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: unset;
  }
}

.show {
  opacity: 1 !important;
  transition: opacity 0.3s ease-in;
}

.hide {
  opacity: 0 !important;
  transition: opacity 0.3s ease-out;
}

a {
  text-decoration: underline;
}

.card-error {
  padding: 5px 10px;
  background: #981515;
  border-radius: 5px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #ffffff;
}

.error-link {
  @extend .card-error;
}

.box-permission {
  background: rgba(248, 248, 248, 0.82);
  backdrop-filter: blur(25);
  border-radius: 12px;
  width: 100%;
  height: auto;
  margin: 0 auto 20px;
  margin-top: 8px;

  @media screen and (max-width: 360px) {
    width: calc(100% + 20px);
    margin-left: -10px;
  }
  .keyline-horizontal {
    background: linear-gradient(0deg, #3f3f3f, #3f3f3f), rgba(0, 0, 80, 0.05);
    background-blend-mode: color-burn, normal;
    transform: matrix(1, 0, 0, -1, 0, 0);
    width: 100%;
    height: 0.5px;
  }
  .keyline-vertical {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(0deg, #3f3f3f, #3f3f3f), rgba(0, 0, 80, 0.05);
    background-blend-mode: color-burn, normal;
    transform: matrix(1, 0, 0, -1, 0, 0);
    width: 0.5px;
    height: 100%;
  }
}

.star-twinkle {
  transform: translateY(-15px) scale(0);
  animation: starAnimal 0.8s infinite alternate;
  @for $x from 1 through 9 {
    &:nth-child(#{$x}) {
      animation-delay: 80ms * ($x - 1);
    }
  }
}

@keyframes starAnimal {
  from {
    transform: translateY(-15px) scale(0);
  }
  to {
    transform: translateY(-15px) scale(1);
  }
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ff4242;
  border: 2px solid #ff424280;
}

.input-search {
  .label {
    z-index: 10;
    color: rgb(255, 255, 255, 0.5) !important;
  }
  .q-field__inner {
    z-index: 9;
    background: #2e3b54;
    border-radius: 4px;
  }
  .q-input {
    border-radius: 4px !important;
    border: 1px solid transparent !important;
    background-clip: padding-box !important;
    position: relative !important;
    &:before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 0;
      margin: -1px !important;
      border: none !important;
      border-radius: inherit !important;
      background: #364c76 !important;
      pointer-events: none;
    }
    &:after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 0;
      margin: 0 !important;
      border-radius: inherit !important;
      background: #0d1a32;
      pointer-events: none;
    }
  }
}

.frame-discount-powerup {
  position: relative;
  padding: 10px 40px;
  margin-left: -35px;
  width: calc(100% + 70px);
  background: linear-gradient(88deg, #bc18d7 40.21%, #a150f1 98.03%);
  pointer-events: none;
  &::before {
    position: absolute;
    content: '';
    background-image: url('/imgs/frame-star-bottom.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 21px;
    height: 22px;
    bottom: -5px;
    left: 5px;
  }
  &::after {
    position: absolute;
    content: '';
    background-image: url('/imgs/frame-star-top.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 26px;
    height: 30px;
    top: -5px;
    right: 5px;
  }
}

.fullscreen {
  z-index: 6000;
  border-radius: 0 !important;
  max-width: 100svw !important;
  max-height: 100svh !important;
}

@keyframes wiggle {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  30% {
    transform: translate(0, 0) rotate(0deg);
  }
  40% {
    transform: translate(0, -5px) rotate(2deg);
  }
  50% {
    transform: translate(0, -5px) rotate(-2deg);
  }
  60% {
    transform: translate(0, -5px) rotate(2deg);
  }
  70% {
    transform: translate(0, -5px) rotate(-2deg);
  }
  100% {
    transform: translate(0, 0) rotate(0);
  }
}

.jump-wiggle {
  animation: wiggle 2s infinite;
}

@keyframes scale-infinite {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.animate-scale-infinite {
  animation: scale-infinite 2s infinite;
}
