.o-custom-btn {
  padding: 2.5px;
  max-width: 285px;
  text-transform: unset !important;

  &.loading,
  &.disabled {
    opacity: 0.5 !important;
    pointer-events: none;
  }
  &.q-btn:before {
    box-shadow: unset;
    border-bottom: unset;
  }
  .q-ripple {
    border-radius: 8px;
  }

  .q-btn__content {
    position: relative;
  }
  .q-btn__content > span {
    position: relative;
    z-index: 2;
  }
  .custom-btn {
    padding: 0 10px;
    z-index: 1;
    text-transform: unset;
    border-radius: 0;
    height: 100%;
    width: 100%;
    border-image-slice: 8 !important;
    border-image-width: 4px !important;
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    min-height: auto;
    &::before {
      display: none;
    }
  }
  &.o-square {
    border: none !important;
    min-width: 40px !important;
    min-height: 40px !important;
    width: 40px !important;
    height: 40px !important;
  }
  &.o-medium-square {
    border: none !important;
    min-width: 45px !important;
    min-height: 45px !important;
    width: 45px !important;
    height: 45px !important;
  }
  &.o-custom {
    box-sizing: border-box;
    border-radius: 0;
    border-image-slice: 6.4;
    border-image-repeat: round;
    border-image-width: 3px;
    border-image-source: url('/imgs/button/oho.png');
  }
  &.o-primary {
    @extend .o-custom;
  }
  &.o-purple {
    @extend .o-custom;
    border-image-source: url('/imgs/button/outer_tim.png');
  }
  &.o-secondary {
    @extend .o-custom;
    border-image-source: url('/imgs/button/outer_secondary.png');
  }

  &.o-medium {
    min-width: 150px;
  }

  &.o-max-content {
    min-width: fit-content !important;
    border-image-slice: 10.5;
    border-image-width: 4px !important;
  }

  &.o-small {
    height: 36px !important;
    max-height: 36px;
    min-height: 36px;
    min-width: 60px;
    border-image-slice: 10.5;
    border-image-width: 4px !important;
  }

  .custom-btn {
    &.primary {
      background: linear-gradient(
        180deg,
        rgba(56, 210, 231, 0.6) 0%,
        #165a73 100%
      );
      border-radius: 7.5px !important;
      box-sizing: border-box;
      border-radius: 0;
      border-image-slice: 6.4;
      border-image-repeat: round;
      border-image-width: 3px;
      border-image-source: url('/imgs/button/inner_primary.png');
    }
    &.custom {
      @extend .primary;
      background: linear-gradient(180deg, #38d2e7 -36.76%, #165a73 107.35%);
      border-image-source: url('/imgs/button/inner_primary.png');
    }
    &.purple {
      @extend .primary;
      border-image-source: url('/imgs/button/inner_tim.png');
      background: linear-gradient(
        180deg,
        rgba(147, 75, 218, 0.8) 0%,
        #511d85 100%
      );
    }
    &.secondary {
      @extend .primary;
      border-image-source: url('/imgs/button/inner_secondary.png');
      background: linear-gradient(
        180deg,
        rgba(200, 107, 233, 0.8) 0%,
        #bb20c9 100%
      );
    }

    &.primary-square {
      background: url('/imgs/button/square.png') !important;
      background-position: center center !important;
      background-size: 100% 100% !important;
    }

    &.secondary-square {
      background: url('/imgs/button/square_sec.png') !important;
      background-position: center center !important;
      background-size: 100% 100% !important;
    }
    &.neon-square {
      background: url('/imgs/button/square_neon.png') !important;
      background-position: center center !important;
      background-size: 100% 100% !important;
    }

    //Size

    &.max-content {
      padding: 0 10px;
      min-width: fit-content !important;
      border-image-slice: 10.5 !important;
      border-image-width: 4px !important;
    }

    &.small {
      border-image-slice: 10.5 !important;
      border-image-width: 4px !important;
    }

    //Shape

    &.square {
      border: none;
      padding: 0 !important;
    }

    & .amount {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      padding: 0px 8px;
      height: 30px;
      background: #083e51;
      border-radius: 5px;
    }
  }
}
.btn-back-left {
  left: 10px;
  top: 16px;
  z-index: 1;
}

.btn-back-left-dialog {
  left: -10px;
  top: -8px;
  z-index: 1;
}
.btn-back-right-dialog {
  right: -10px;
  top: -8px;
  z-index: 1;
}
.btn-mini {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  max-height: 28px !important;
  max-width: 28px;
  min-height: 28px;
  min-width: 28px;
  padding: 0 !important;
}
.btn-success {
  @extend .btn-mini;
  background: linear-gradient(180deg, #1ccbb8 -8.75%, #008c96 100%);
  border: 0.5px solid #0e8a8f;
  box-shadow: inset 0px -2px 0px rgba(0, 0, 0, 0.1);
}
.btn-error {
  @extend .btn-mini;
  background: linear-gradient(180deg, #e93539 -8.75%, #7e0d0d 100%);
  border: 0.5px solid #873333;
  box-shadow: inset 0px -2px 0px rgba(0, 0, 0, 0.1);
}
.btn-option {
  @extend .btn-mini;
  background: linear-gradient(180deg, #6c44c4 -8.75%, #41267b 100%),
    linear-gradient(180deg, #1ccbb8 -8.75%, #008c96 100%);
  border: 0.5px solid #333587;
  box-shadow: inset 0px -2px 0px rgba(0, 0, 0, 0.1);
}
