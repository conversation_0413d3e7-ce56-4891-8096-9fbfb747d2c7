@use './fonts';
@use './mixin';
@use './global';
@use './button';
@use './input';
@use './typography';
@use './notification';
@use './expand';
@use './checkbox';
@use './slider';
@use './select';
@use './color';
@use './tab';

body {
  margin: 0;
  padding: 0;
  font-family: inherit;
  box-sizing: inherit;
  user-select: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  touch-action: manipulation;
}
body,
html,
#root {
  font-family: 'Karla', sans-serif;
  box-sizing: border-box;
  position: fixed;
  width: 100%;
  height: 100%;
  scroll-behavior: smooth;
  // overflow: hidden;
  color: white;
  text-wrap: pretty;
  background-color: #0f132a;
}

@media screen and (max-width: 374px) {
  body,
  html,
  #root {
    font-size: 90%;
  }
}

:root {
  background: #0f1229;
}
