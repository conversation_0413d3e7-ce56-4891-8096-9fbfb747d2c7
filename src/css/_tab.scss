.tab_v2 {
  background: #222f47;
  border-radius: 5px 5px 0px 0px;
  height: 38px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.5);

  .q-tab {
    border-radius: 5px 5px 0px 0px;
    padding: 0;
    border-bottom: 1px #11d1f9 solid;
    text-transform: none !important;
    &--active {
      border-bottom: none !important;
      border-top: 1px #11d1f9 solid;
      border-right: 1px #11d1f9 solid;
      border-left: 1px #11d1f9 solid;
      background: #091a3b;
      color: white;
    }
    &__indicator {
      display: none;
    }
  }
}
.tab_panel_v2 {
  background: linear-gradient(#091a3b, #091a3b),
    linear-gradient(360deg, rgb(9, 38, 70) 0%, rgb(16, 178, 217) 100%);
  background-size: calc(100% - 2px) calc(100% - 1px), 100% 100%;
  background-position: center top, center;
  background-repeat: no-repeat;
  position: relative;
  border-radius: 0px 0px 5px 5px;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 1px;
  //   right: 1px;
  //   bottom: 1px;
  //   background-color: #091a3b;
  //   border-radius: 0px 0px 5px 5px;
  //   z-index: 1;
  // }
  .q-panel {
    z-index: 2;
  }
}
