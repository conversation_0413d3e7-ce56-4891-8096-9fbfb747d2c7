@mixin fit {
  & {
    width: 100%;
    height: 100%;
  }
}

@mixin flex-col {
  & {
    display: flex;
    flex-direction: column;
  }
}

@mixin flex-center {
  & {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

@mixin fullscreen {
  & {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
  }
}

@mixin transitionFor($trans, $props...) {
  & {
    @if length($props) == 0 {
      transition: $trans;
      will-change: list.nth($trans, 1);
    } @else if length($props) == 1 {
      transition: $props $trans;
      will-change: $props;
    } @else {
      transition: $trans;
      transition-property: $props;
      will-change: $props;
    }
  }
}
