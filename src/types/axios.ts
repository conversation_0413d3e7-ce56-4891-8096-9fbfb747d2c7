export type ErrorMsgType =
  | 'verify_coin_locked'
  | 'mobile_number_existed'
  | 'invalid_otp'
  | 'invalid_password'
  | 'otp_locked'
  | 'banned_number'
  | 'invalid_credentials'
  | 'invalid_mobile_number'
  | 'otp_limited'
  | 'coin_forfeited'
  | 'no_coin'
  | 'no_event'
  | 'temp_locked'
  | 'expired'
  | 'speed_limit_exceeded'
  | 'cancelled'
  | 'tada_already_used_by_other'
  | 'not_in_sentosa'
  | 'crystal_detector_timeout'
  | 'metal_detector_timeout'
  | 'not_in_time'
  | 'not_checked_in'
  | 'not_claimed'
  | 'eligible'
  | 'not_in_zone'
  | 'not_in_range'
  | 'claimed'
  | 'Invalid value';

export interface APIResponseDataInfo {
  remaining_attempts?: number;
  lock_until?: string;
  redeemed_at?: string;
}

export interface APIResponseData {
  info: APIResponseDataInfo;
  failed: number;
  lock_until: string;
  attempts?: number;
  required_distance: number;
}
export interface APIResponseError {
  data: APIResponseData;
  error_message: ErrorMsgType;
  status: string;
  code: number;
}

export type APISVInfoType =
  | 'no_email'
  | 'locked'
  | 'temp_locked'
  | 'resend_limited'
  | 'incorrect_pin'
  | 'invalid_code'
  | 'otp_expired'
  | 'server_interrupted'
  | 'rejected'
  | 'outlet_invalid'
  | 'user_inactive'
  | 'exist_email';

export interface APISVResponseData {
  info: APISVInfoType;
  failed: number;
  locked_until: string;
  attempts?: number;
  verify: boolean;
}

export interface APISVResponseError {
  data: APISVResponseData;
  error_message: ErrorMsgType;
  status: string;
  code: number;
  message: string;
}
