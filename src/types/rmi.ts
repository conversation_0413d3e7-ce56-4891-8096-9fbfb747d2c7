export type RmiOutletOpeningHours =
  | 'monday'
  | 'tuesday'
  | 'wednesday'
  | 'thursday'
  | 'friday'
  | 'saturday'
  | 'sunday';

export type RmiOutletType =
  | 'poi'
  | 'normal'
  | 'mega'
  | 'landmark'
  | 'merchant_rmi';

export interface RmiOutlet {
  source: string;
  type: RmiOutletType;
  unique_id: string;
  brand_unique_id: string;
  name: string;
  address: string;
  location: {
    lat: number;
    lng: number;
  };
  fun_facts: string[];
  did_you_know: string[];
  description: string[];
  images: string[];
  open_hours: Record<RmiOutletOpeningHours, string>;
  distance: number;
  icon: string;
  bg: string;
  badge: string;
  badge_id: string;
}

export interface RmiAds {
  unique_id: string;
  brand_unique_id: string;
  type: string;
  link: string;
  title: string;
  description: string;
}
