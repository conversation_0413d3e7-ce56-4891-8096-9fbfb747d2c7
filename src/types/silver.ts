import { BrandSponsors } from '@enums';
import { BaseLocation, CoinStatusType, CoinType, WinnerInfoPayload } from '@types';

export interface ICircle {
  center: BaseLocation;
  radius: number;
}

export interface SilverCoin {
  _id: string;
  coin_id: string;
  name: string;
  status: CoinStatusType;
  start_at: string;
  end_at: string;
  winner_info: WinnerInfoPayload;
  canUsePowerUp: boolean;
  circle: ICircle;
  lastCircle: ICircle;
  freeCircle: ICircle;
  paidCircle: ICircle;
  nextShrinkAt: string;
  is_smallest_public_circle: boolean;
  coin_number: number;
  sponsored_ba_uid: string;
  order: number;
  time_up: boolean;
  fake: boolean;
  hide_at: string;
  sponsor: boolean;
  amenities: boolean;
  beacon: boolean;
  location: BaseLocation;
  verification_started_at: string;
  found_at: string;
  videos: string[];
  verifyingCircle: ICircle;
  forfeited_at: string;
  type: CoinType;
  serial_number: string;
  submit_info_at: string;
  user: string;
  winner_name: string;
  distance: number;
  brand_unique_id: BrandSponsors;
  reward: string;
  brand_name: string;
  show_lure_text: boolean;
  radius: number;
  geohash: string;
  isGoldenCoin: boolean;
  special_event: boolean;
}

export interface RetrieveSilverCoin {
  coin: SilverCoin;
}

export interface ShrinkSilverCoin {
  totalTimeShrank: number;
}

export type SilverCoinVerificationType = 'silver_coin' | 'golden_coin';

export interface SilverCoinVerification {
  coin: {
    _id: string;
    serial_number: string;
    coin_id: string;
    status: CoinStatusType;
    coin_number: number;
    brand_unique_id: string;
    reward: string;
    brand_name: string;
    coin_name: string;
  };
  submit_info_at: string;
  _id: string;
  type: SilverCoinVerificationType;
  coin_id: string;
  coin_number: number;
  brand_unique_id: string;
  brand_name: string;
  reward: string;
  winner_info: WinnerInfoPayload;
  fromMap: boolean;
  coin_name: string;
}

export interface SilverWinnerInfo {
  first_name: string;
  last_name: string;
  nric: string;
  mobile_number: string;
}
