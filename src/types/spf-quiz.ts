import { UserRewards } from '@types';

export interface SpfQuizExplanation {
  correct_answer: string;
  wrong_answer: string;
}

export interface ISpfQuiz {
  question_id: string;
  question: string;
  options: string[];
  explanation_content: SpfQuizExplanation;
  explanation_title: SpfQuizExplanation;
}

export interface SpfQuizData {
  quizzes: ISpfQuiz[];
  crime_advisory: string;
  quiz_completed_at: string | null;
  corrected_questions: string[];
  ba_group_id: string;
  crystal_per_question: number;
  quiz_ba_unique_id: string;
  sharing_ba_unique_id: string;
  shared_at: string;
  claimed_at: string;
}

export interface SpfQuizAnswer {
  correct: boolean;
}

export interface SpfQuizShare {
  action_success: boolean;
}

export interface SpfQuizReward {
  reward: UserRewards;
}
