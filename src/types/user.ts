import { ComponentId, Season, SeasonFeatures } from '@types';

export type TViableMapLayer = 'all' | 'golden' | 'silver';
export type TUserLang = 'en' | 'vi';

export type TOnboarding =
  | 'first_entry'
  | 'promo_code'
  | 'tap_timii'
  | 'golden_coin_verifying'
  | 'golden_coin_found'
  | 'left_10_grids'
  | 'all_coins_found'
  | 'gold_coin_available'
  | 'during_sudden_death'
  | 'before_sudden_death'
  | 'gps'
  | 'survey_trigger'
  | 'first_sqkii_vouchers'
  | 'onboarding_journey'
  | 'shrink_silver_coin'
  | 'first_beacon'
  | 'crystal_expiring'
  | 'first_metal_detector'
  | 'first_metal_sonar'
  | 'first_crystal_detector'
  | 'first_time_claim_crystal_detector';

export interface UserSetting {
  background_music: number;
  sound_effect: number;
  viable_map_layer: TViableMapLayer;
  pedometer_goal: number;
  pedometer: boolean;
}

export interface UserResources {
  crystal: number;
  beacon: number;
  sentosa_crystal_detector: number;
}

export interface UntameAccount {
  synced_at: string;
  uid: string;
  untame_id: string;
}

export interface User {
  id: string;
  banned: boolean;
  hunter_id: string;
  mobile_number: string;
  verified_mobile_number_at: string;
  sv_token: string;
  referral_code: string;
  expire_at: string;
  next_otp_at: string;
  created_at: string;
  session_number: number;
  lang: TUserLang;
  setting: UserSetting;
  onboarding: Record<TOnboarding, boolean>;
  resources: UserResources;
  coin_drop: string[];
  silver_coin_found?: number;
  total_coin_submissions: number;
  total_gold_coin_submissions: number;
  claimed_welcome_reward: boolean;
  last_claim_bonus: string;
  claimed_daily_reward: string[];
  shrink_cooldown: Record<string, string>;
  coin_lock_until: string;
  metal_detector_cooldown: string;
  verify_coin_lock_until?: string;
  change_hunter_id_attempts?: number;
  verify_coin_attempts?: number;
  tiger_permission_allowed?: boolean;
  free_eliminate?: boolean;
  free_hint?: boolean;
  survey?: boolean;
  end_survey?: boolean;
  untame_account: UntameAccount;
  metadata?: Record<string, any>;
}

export interface ResendOTP {
  next_otp_at: string;
  expire_at?: string;
}

export interface GuestUser {
  token: string;
  user: User;
}

export interface ChangeHunterIdFee {
  attempt?: number;
  amount?: number;
  hunter_id: string;
  next_fee: number;
}

export interface BAMultiplier {
  featured: number;
  first_time: number;
}

export interface BASettings {
  multiplier: BAMultiplier;
}

export interface ZaloDailyQuota {
  remainingQuota: number;
  dailyQuota: number;
}

export interface GoldenCoinLocation {
  lat: number;
  lng: number;
}

export type TGoldenCoinStatus = 'ongoing' | 'verifying' | 'found' | 'scheduled';

export interface GoldenCoin {
  status: TGoldenCoinStatus;
  start_verify_at: string;
  found_at: string;
  videos: string[];
  winner_name: string;
  geohash: string;
  location: GoldenCoinLocation;
}

export interface FrontendBuild {
  version: string;
  last_update: string;
}

export interface SettingsDates {
  season_start_at: string;
  blocker_countdown_start_at: string;
  sentosa_drop_hint_at: string;
  sentosa_beach_station_event_start_at: string;
  sentosa_beach_station_event_end_at: string;
  start_state_1: string;
  start_state_2: string;
  start_state_3: string;
  start_state_4: string;
  end_state_1: string;
  end_state_2: string;
  end_state_3: string;
  end_state_4: string;
}

export interface BeaconSettings {
  radius: number;
}

export interface MetalDetector {
  active_at: string;
  inactive_at: string;
  duration_in_sec: number;
  extend_duration_in_sec: number;
  max_speed: number;
  max_speed_warning_times: number;
  extend_offer_ttl: number;
}

export interface SettingsFlags {
  last_10_grids: boolean;
  all_coins_found: boolean;
  perpetual_hunt: boolean;
  end_game_survey?: boolean;
  crystal_coin_end?: boolean;
  sentosa_coin_end?: boolean;
  capita_land_coin_end?: boolean;
  hide_beach_station_map_icon: false;
}

export interface CrystalDetector {
  duration_in_sec: number;
  max_speed: number;
  max_speed_warning_times: number;
}

export interface IslandBounty {
  start_at: string;
  end_at: string;
}

export interface ISentosaSettings {
  crystal_detector: CrystalDetector;
  beach_station_hint_2_required: number;
  island_bounty: IslandBounty;
}

export type THoldingPageState = 'none' | 'silver' | 'gold' | 'gold_ongoing' | 'silver_ongoing';

export interface Settings {
  eliminate_grid_price: number;
  season_start_at: string;
  shrink_circle_price: number;
  text_hint_price: number;
  change_hunt_id_fee: ChangeHunterIdFee[];
  survey_crystal_reward: number;
  referral_reward: number;
  next_eliminate_at: string;
  grids_per_elimination: number;
  panda_mart_ba: string;
  gps_off_ba: string;
  holding_page_target_url: string;
  holding_page_state: THoldingPageState;
  zalo_daily_quota: ZaloDailyQuota;
  golden_coin: GoldenCoin;
  ms_cooldown_to_lock: number;
  ongoing_coin_remaining: number;
  silver_found_dialog: boolean;
  gold_found_dialog: boolean;
  show_fake_circles: boolean;
  frontend_build: FrontendBuild;
  seasons: Season[];
  dates: SettingsDates;
  hardcoded_contest_unique_id: string;
  supported_languages: string[];
  metal_sonar?: {
    start_at: string;
    end_at: string;
  };
  beacon: BeaconSettings;
  metal_detector: MetalDetector;
  features: SeasonFeatures;
  flags: SettingsFlags;
  brand_action: BASettings;
  sentosa: ISentosaSettings;
}

export interface BrandSOVSettings {
  brands: Record<string, string>;
  probs: Record<ComponentId, Record<string, number>>;
  asset_packs: Record<string, string>;
}

export interface AdventureLogs {
  state: 'ended' | 'ongoing' | 'future';
  key: string;
  content: string;
  location: {
    lng: number;
    lat: number;
  };
  order_number: number;
}

export interface PedometerDailyProgress {
  weekday: 'Mon' | 'Tue' | 'Wed' | 'Thu' | 'Fri' | 'Sat' | 'Sun';
  date: string;
  steps: number;
  distance: number;
}

export type TransactionReasonType =
  | 'use_shrink_power_up'
  | 'brand_action'
  | 'admin'
  | 'eliminate_pu'
  | 'buy_text_hint'
  | 'referral'
  | 'change_hunter_id'
  | 'survey'
  | 'welcome_reward'
  | 'online_time_bonus'
  | 'daily_reward'
  | 'daily_mission'
  | 'untame_spent'
  | 'brand_action_milestone'
  | 'vote_contest'
  | 'redeem_code'
  | 'use_metal_sonar'
  | 'use_metal_detector'
  | 'sqkii_voucher'
  | 'skip_mission'
  | 'timed_mission'
  | 'sentosa_daily_reward'
  | 'sentosa_island_bounty';

export interface Transaction {
  amount: number;
  created_at: string;
  metadata: Record<string, unknown>;
  mission_unique_id: string;
  reason: TransactionReasonType;
  resource_type: string;
}

export interface CrystalExpiring {
  crystal_expiring: {
    date: string;
    expiring_amount: number;
  };
}

export interface SqkiiVoucherReferral {
  max: number;
  current: number;
}

export interface UserReferral {
  id: string;
  referee: {
    hunter_id: string;
    _id: string;
    mobile_number: string;
    verified_mobile_number_at: string;
  };
  status: 'claimed' | 'pending' | 'verified';
}

export type MerchantAcquisitionType = 'friends' | 'owners';
export interface MerchantAcquisition {
  visited: number;
  closed_callout: number;
  max_submit: number;
  total_submit: number;
  show_callout: boolean;
  last_type: MerchantAcquisitionType;
  submitted: boolean;
}
