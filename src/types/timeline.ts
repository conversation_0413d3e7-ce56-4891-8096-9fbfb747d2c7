import { BaseLocation } from '@types';

export type TimelineLogsStatus = 'ongoing' | 'future' | 'ended';

export interface TimelineLogs {
  _id: string;
  status: TimelineLogsStatus;
  video_links?: string[];
  registered_at?: string;
  background_image: string;
  time: string;
  copy_position_5: string;
  copy_position_4: string;
  city: string;
  copy_position_2: string;
  country_code: string;
  copy_position_1: string;
  copy_position_3: string;
  order: number;
  hunt_name: string;
  season_id: string;
  logo: string;
  adventure_log_content: string;
  adventure_log_date: string;
  adventure_log_location: BaseLocation;
  adventure_log_title: string;
}
