import { SqkiiVouchersTransactionStatus, SqkiiVouchersTransactionType } from '@enums';

export interface SVUserBalance {
  balance: number;
  currency: string;
}

export interface SVRewardedPoints {
  crystals: number;
  beacon: number;
}

export interface SdkLinking {
  hunter_id: string;
  user_id: string;
}

export interface ExtendedSdkLinking extends SdkLinking {
  mobile_number: string;
}

export interface SVCampaign {
  campaign: string;
}

export interface SVUserOnboarding {
  tutorial: boolean;
  level: boolean;
  topup_survey: string;
  payment_survey: string;
}

export type SVUserSettings =
  | 'top-up'
  | 'payment'
  | 'bytes_conversion'
  | 'missions'
  | 'new_games'
  | 'announcements';

export interface SVPlaceOfResidence {
  country: string;
  city: string;
  address_1: string;
  address_2: string;
  unit_number: string;
  postal_code: string;
}

export interface SVNotificationSettings {
  app: true;
  email: true;
}

export interface SVUser extends SVUserBalance {
  id: string;
  onboarding: SVUserOnboarding;
  username: string;
  email: string;
  status: string;
  enable_transmute: boolean;
  referral_code: string;
  mobile_number: string;
  hasPin: boolean;
  wallet_id: string;
  migrated: boolean;
  can_migrate: boolean;
  mobile_number_verified: boolean;
  sync_htm: boolean;
  date_of_birth: string;
  my_game_lib: string;
  place_of_residence: SVPlaceOfResidence;
  setting_notification: Record<SVUserSettings, SVNotificationSettings>;
  enable_biometric: true;
  total_topup: number;
  profile_completed: false;
  level: number;
  nextLevel: number;
  level_reward: number;
  accumulated_topup: number;
  exp: number;
  country: string;
}

export interface SVUserLogin {
  token: string;
  user: SVUser;
}

export interface SVUserMe {
  user: SVUser;
}

export interface SVTopUpInfo {
  id: string;
  payment_id: string;
  status: string;
  amount: string;
  bytes: number;
  currency: string;
  expiry_at: string;
}

export interface SVTopUp {
  confirm_link: string;
  redirect_url: string;
  info: SVTopUpInfo;
}

export interface SVTopUpDetails {
  amount: number;
  balance: number;
  bonus_bytes: number;
  bytes: number;
  country: string;
  created_at: string;
  currency: string;
  id: string;
  method: string;
  payment_id: string;
  status: string;
  timestamp: string;
  topup_id: string;
  type: string;
  updated_at: string;
  user_id: string;
  rewarded: SVRewardedPoints;
}

export interface SVOutletLocation {
  type: string;
  coordinates: [number, number];
}

export interface SVOutlet {
  id: string;
  code: string;
  name: string;
  type: string;
  image: string;
  country: string;
  currency: string;
  description: string;
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
  sunday: string;
  weekday: string;
  weekend: string;
  public_holiday: string;
  eve_holiday: string;
  address: string;
  location: SVOutletLocation;
  company_name: string;
  postal_code: string;
  distance: number;
  brand_type: string;
  min_spend: number;
}

export type SVOutletsData = Record<string, SVOutlet[]>;

export interface SVRecentOutlet {
  code: string;
  name: string;
  processed_at: string;
}

export interface SVRecentOutletsData {
  recent: SVRecentOutlet[];
  outlets: SVRecentOutlet[];
}

export interface SVPaymentResult {
  country: string;
  amount: number;
  user_id: string;
  username: string;
  outlet_id: string;
  outlet_name: string;
  company_id: string;
  merchant_id: string;
  owner_id: string;
  txn_id: string;
  payment_id: string;
  status: string;
  type: string;
  currency: string;
  completed_at: string;
  rewarded: Pick<SVRewardedPoints, 'crystals'>;
}

export interface SVTransactionLinked {
  hunter_id: string;
  user_id: string;
  link_at: string;
}

export interface SVTransactionHistory {
  id: string;
  user_id: string;
  txn_id: number;
  type: SqkiiVouchersTransactionType;
  outlet_name: string;
  payment_id: string;
  amount: number;
  currency: string;
  country: string;
  status: SqkiiVouchersTransactionStatus;
  created_at: string;
  updated_at: string;
  rewarded: SVRewardedPoints;
  linked: SVTransactionLinked;
}

export interface SVTransactionHistoryData {
  data: SVTransactionHistory[];
  total: number;
}

export interface SVUsedPromoCode {
  promo_code: string;
  bonus_crystals: number;
}

export interface SVForgotPassword {
  resend_after: string;
  resendTimes: number;
}

export interface SVCheckCredential {
  email: string;
  mobile_number: string;
  username: string;
}

export interface SVPin {
  verify: boolean;
}

export interface SVRateRewards {
  crystals: number;
}
