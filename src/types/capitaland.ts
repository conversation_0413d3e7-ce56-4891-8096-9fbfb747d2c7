export type CapitalandRewardType = 'crystal' | 'capitaland_hint';

export type CapitalandSpecialCoinStatus =
  | 'ongoing'
  | 'verifying'
  | 'found'
  | 'forfeited'
  | 'scheduled';

export interface CapitalandRewardData {
  hint_number: string;
}

export interface CapitalandHint {
  coin_name: string;
  content: string;
}

export interface CapitalandDailyReward {
  day: number;
  reward_type: CapitalandRewardType;
  amount: number;
  reward_data: CapitalandRewardData;
  used_at: string;
  claimed_at: string;
  hint: CapitalandHint;
}

export interface SentosaHint {
  _id: string;
  order: number;
  is_social_media: boolean;
  content: string;
  unlocked_at: string;
}

export interface Location {
  lat: number;
  lng: number;
}

export interface CapitalandWinnerInfo {
  winner_name: string;
}

export interface CapitalandSpecialCoin {
  _id: string;
  name: string;
  status: CapitalandSpecialCoinStatus;
  unique_id: string;
  order: number;
  locked: boolean;
  hints: SentosaHint[];
  drop_at: string;
  video_link: string;
  winner_info: CapitalandWinnerInfo;
  forfeited_at: string;
  found_at: string;
  verifying_at: string;
  location: Location;
}

export interface DisplayOpeningHours {
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
  sunday: string;
}

export interface GeoLocation {
  type: 'Point';
  coordinates: [number, number];
}

export interface CapitalandAmenity {
  _id: string;
  unique_id: string;
  name: string;
  address: string;
  description: string;
  mission_title: string;
  promo_text: string;
  icon: string;
  images: string[];
  location: GeoLocation;
  computed_opening_hours: number[][];
  display_opening_hours: DisplayOpeningHours;
  reward: number;
  claimed_at: string | null;
  closed_at: string | null;
  canCheckIn: boolean;
  distanceInMeters: number | null;
  amenities: boolean;
}
