export type MediaType = 'media' | 'media_custom_size' | 'media_with_background';

export type TextType = 'text' | 'text_with_purple_frame';

export type BodyContentType = 'heading' | 'title' | TextType | MediaType;

export type TriggerAction = 'close';
export type NotificationStatus = 'draft' | 'published' | 'archived';
export type FrequencyType = 'custom' | 'every_load' | 'once';
export type FooterActionType = 'button' | 'button_link' | 'checkbox';

export interface HeaderNotification {
  close_able?: boolean;
  icon?: boolean;
  icon_url?: string;
}

export interface FooterActionData {
  key?: string;
  link?: string;
}

export interface FooterAction {
  id?: string;
  variant?: any;
  data?: FooterActionData;
  trigger?: TriggerAction;
  type: FooterActionType;
}

export interface FooterNotification {
  bottom_frame?: boolean;
  frame_url?: string;
  actions?: FooterAction[];
}

export interface BodyNotificationData {
  key?: string;
  media_url?: string;
  bg_url?: string;
  media_size?: string;
  frame_url?: string;
}

export interface BodyNotification {
  id: string;
  type: BodyContentType;
  data?: BodyNotificationData;
}

export interface FrequencySettings {
  interval: number;
  period: number;
}

export interface SettingNotification {
  from: string;
  to: string;
  frequencyType: FrequencyType;
  frequency: FrequencySettings;
  unique_id: string;
}

export interface Notification {
  _id: string;
  should_show: boolean;
  template: string;
  setting: SettingNotification;
  header: HeaderNotification;
  body: BodyNotification[];
  footer: FooterNotification;
  status: NotificationStatus;
}
