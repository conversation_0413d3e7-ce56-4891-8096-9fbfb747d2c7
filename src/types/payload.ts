import { BaseLocation, ExtendedSdkLinking, SdkLinking, SVCampaign } from '@types';

export interface LoginPayload {
  mobile_number: string;
  password: string;
  captcha_token: string;
}

export interface SignUpPayload extends LoginPayload {
  otp: string;
}

export interface VerifyMobileNumberPayload {
  otp?: string;
}

export interface ResendOTPPayload {
  mobile_number: string;
  type: 'register' | 'forgot_password';
  captcha_token: string;
}

export interface TrackingPayload {
  id: string;
  action: string;
  data?: Record<string, unknown>;
}

export type UserSettingType =
  | 'background_music'
  | 'sound_effect'
  | 'viable_map_layer'
  | 'pedometer_goal'
  | 'pedometer';

export interface UserSettingPayload {
  type: UserSettingType;
  value: any;
}

export interface ReadAnnouncementPayload {
  id: string;
  skip?: boolean;
}

export interface TrackUserBaseLocation extends BaseLocation {
  type?: string;
  coins?: Array<{ coin_id: string; radius: number }>;
  metadata?: Record<string, unknown>;
}

export interface TigerBrokerTokenPayload {
  code: string;
  state: string;
}

export interface SqkiiVoucherReferralPayload {
  name: string;
  email: string;
  mobile_number: string;
  name_of_business: string;
}

export interface BeaconPayload extends BaseLocation {}

export interface PhysicalStorePayload extends BaseLocation {}
export interface LocationBasedPayload extends BaseLocation {}
export interface SpfQuizPayload extends BaseLocation {
  ba_group_id: string;
}

export interface SpfQuizAnswerPayload {
  ba_group_id: string;
  question: string;
  option: string;
}

export interface AmenitiesCheckInPayload extends BaseLocation {
  unique_id: string;
}

export interface IDailyMissionWalkPayload extends BaseLocation {
  steps: number;
}

export interface BuyHintPayload extends BaseLocation {
  quantity: number;
}

export interface WinnerInfoPayload {
  serial_number: string;
  first_name: string;
  last_name: string;
  mobile_number: string;
  accepted_tac: boolean;
  sent_video: boolean;
  nric: string;
  bank_name: string;
  bank_code: string;
  branch_code: string;
  bank_account_number: string;
  bank_account_name: string;
  currency: string;
  country: string;
  submitted_mailing_proof: boolean;
  reached_minimum_age: boolean;
  parent_first_name: string;
  parent_last_name: string;
  parent_nric: string;
  parent_mobile_number: string;
  fulfillment_date: string;
  fulfillment_type: 'bank' | 'cash';
  prize_amount: string;
}

export interface ShrinkSilverCoinPayload extends BaseLocation {
  coin_id: string;
}

export interface GoldenEliminatedPayload extends BaseLocation {
  quantity: number;
}

export interface MetalDetectorPayload extends BaseLocation {
  item_id?: string;
}

export interface MetalSonarPayload extends BaseLocation {
  radius: number;
  id: string;
}

export interface MetalSonarPricePayload extends BaseLocation {
  id: string;
}

export interface ReminderPayload {
  email: string;
  season_id: string;
  type: 'brand' | 'player';
}

export interface SVLoginPayload {
  email: string;
  password: string;
  sdk_linking: ExtendedSdkLinking;
}

export interface SVActivateOTPPayload {
  email: string;
  code: string;
}

export interface SVChangeMailPayload {
  otp_code: string;
  email?: string;
}

export interface SVCreateAccountPayload {
  email: string;
  password: string;
  mobile_number: string;
  country: string;
  sdk_linking: SdkLinking;
}

export interface SVSetPasswordPayload {
  recover_token: string;
  password: string;
}

export interface SVCheckCredentialsPayload {
  email: string;
  mobile_number: string;
  country: string;
  sdk_linking: SdkLinking;
}

export interface SVCreateAccountPayload extends SVCheckCredentialsPayload {
  password: string;
}

export interface SVChangePinPayload {
  old_pin: string;
  pin_code: string | undefined;
}

export interface SVSetPinOTPPayload {
  otp_code: string;
  pin_code?: string;
}

export interface SVPayloadChangeMail {
  otp_code: string;
  email: string | undefined;
}

export interface SVTopUpPayload extends SVCampaign {
  amount: number;
  pin_code: string;
  promo_code: string | undefined;
  sdk_linking: Pick<SdkLinking, 'user_id'>;
  campaign: string;
}

export interface SVBonusRatePayload {
  amount: number;
  item: 'crystals';
  promo_code?: string;
}

export interface SVPaymentPayload extends SVCampaign {
  outlet_id: string;
  amount: number;
  pin_code: string;
  sdk_linking: Pick<SdkLinking, 'user_id'>;
}
