import { TUserLang } from '@types';

export interface SeasonFeatures {
  enter_serial_number: boolean;
  referral: boolean;
  brand_action: boolean;
  hints: boolean;
  shrink_power_up: boolean;
  change_hunter_id: boolean;
  survey: boolean;
  signup: boolean;
  multiple_language: boolean;
  legend: boolean;
  login: boolean;
  tac: boolean;
  online_bonus: boolean;
  daily_mission: boolean;
  daily_reward: boolean;
  sqkii_voucher: boolean;
  pedometer: boolean;
  beacon: boolean;
}

export interface FakeCoin {
  coin_id: string;
  coordinates: [number, number];
  hide_at: string;
  show_at: string;
  radius: number;
  order: number;
}

export interface TextBox {
  id: string;
  zoom_level: [number, number];
  type: string;
  location: [number, number];
  anchor: unknown;
  hide_at: string;
  show_at: string;
  metadata?: Record<string, unknown>;
  content: string;
  radius: number;
  angle: number;
}

export type SeasonStatus = 'ongoing' | 'ended' | 'future';

export interface Season {
  status: SeasonStatus;
  hunt_name: string;
  start_at: string;
  end_at: string;
  id: string;
  total_coin: number;
  features: SeasonFeatures;
  time?: string;
  silver_reward: string;
  fake_coins: FakeCoin[];
  text_boxes?: TextBox[];
  lang: TUserLang;
}
