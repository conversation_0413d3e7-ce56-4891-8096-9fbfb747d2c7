<script lang="ts" setup>
import { numeralFormat } from '@utils';
import { useSVStore } from '@stores';
import { useSVBonusRateQuery } from '@services';
import { usePageTracker } from '@composables';
import { useSqkiiVouchers } from '../composables';

interface Props {
  amount?: number;
  hiddenButton?: boolean;
  bonus_crystals?: number;
}

const props = withDefaults(defineProps<Props>(), {
  amount: 10,
  bonus_crystals: 0,
  hiddenButton: false,
});

const storeSV = useSVStore();

const { t } = useI18n();
const { svUser } = storeToRefs(storeSV);
const { closeDialog, openDialog } = useMicroRoute();
const { tracker } = usePageTracker();
const { isFeaturedOffer, multipliers } = useSqkiiVouchers();

const bonusRateQuery = useSVBonusRateQuery(
  computed(() => ({
    amount: props.amount,
    item: 'crystals',
  })),
);

const baseCrystals = computed(() => bonusRateQuery.data.value?.crystals || 0);

const activeMultiplier = computed(() => {
  return isFeaturedOffer.value ? multipliers.value.featured : multipliers.value.firstTime;
});

const headerText = computed(() => {
  return isFeaturedOffer.value
    ? t('SV_FIRST_BONUS_HEADER_1')
    : t('SV_FIRST_BONUS_HEADER', {
        MULTIPLIER: activeMultiplier.value,
      });
});

const descriptionText = computed(() => {
  return isFeaturedOffer.value ? t('SV_FIRST_BONUS_DESC_1') : t('SV_FIRST_BONUS_DESC');
});

const priceDisplay = computed(() => {
  const currency = svUser.value?.currency || 'S$';
  const formattedAmount = numeralFormat(props.amount, '0,0.00');
  return `${currency} ${formattedAmount}`;
});

const crystalRewards = computed(() => {
  const base = baseCrystals.value;
  const originalPrice = isFeaturedOffer.value ? base * multipliers.value.firstTime : base;

  const finalAmount = isFeaturedOffer.value
    ? base * activeMultiplier.value * multipliers.value.firstTime
    : base * activeMultiplier.value;

  return {
    original: numeralFormat(originalPrice),
    final: numeralFormat(finalAmount + props.bonus_crystals),
  };
});

function handlePurchaseClick(): void {
  closeDialog('sqkii_vouchers_first_bonus_crystals');
  openDialog('sqkii_vouchers_get_vouchers');
  tracker({
    id: 'sqkii_vouchers_first_bonus_crystals',
    action: 'click',
    data: {
      target: 'get_vouchers',
    },
  });
}
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="headerText"></div>
    </template>

    <div class="text-sm text-center" v-html="descriptionText"></div>

    <div class="relative w-[calc(100%+48px)] -ml-6 -mt-16 aspect-square">
      <Icon name="big-glow" class="!w-full" />
      <Icon
        class="absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
        name="crystal"
        :size="140"
      />
      <div
        class="absolute top-[60%] left-[45%] -translate-x-1/2 -translate-y-1/2 font-bold text-[40px]"
      >
        {{ activeMultiplier }}x
      </div>
    </div>

    <div
      class="bg-[#091A3B] w-max mx-auto px-5 max-w-full rounded py-2 flex flex-nowrap justify-center items-center text-xl font-bold gap-1 -mt-20 z-10 relative mb-5"
    >
      <div>{{ priceDisplay }}</div>
      <div>=</div>
      <div class="line-through opacity-50">{{ crystalRewards.original }}</div>
      <div>{{ crystalRewards.final }}</div>
      <Icon name="crystal" :size="20" />
    </div>

    <div v-if="!hiddenButton" class="text-center">
      <Button
        track-id="disable-track"
        :label="t('SV_FIRST_BONUS_BTN')"
        class="!w-[200px]"
        @click="handlePurchaseClick"
      />
    </div>
  </Dialog>
</template>
