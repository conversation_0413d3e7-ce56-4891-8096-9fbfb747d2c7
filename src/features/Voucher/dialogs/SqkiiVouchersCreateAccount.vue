<script lang="ts" setup>
import { useFetchQueries, useNow, usePageTracker } from '@composables';
import {
  dateTimeFormat,
  EMAIL_REGEX,
  FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
  successNotify,
  timeCountDown,
} from '@utils';
import { useSVStore, useUserStore } from '@stores';
import { useForm } from 'vee-validate';
import dayjs from 'dayjs';
import * as yup from 'yup';
import {
  useSVAuthActivateOTPMutation,
  useSVAuthCheckCredentialMutation,
  useSVAuthCreateMutation,
  useSVAuthSendActivateMutation,
  useUpdateSVTokenMutation,
} from '@services';
import { APISVResponseError, SVCheckCredentialsPayload } from '@types';

const PASSWORD_REGEX = {
  DIGIT: /\d/,
  LOWERCASE: /[a-z]/,
  UPPERCASE: /[A-Z]/,
  SPECIAL: /[`~!@#$%^&*()\-_+=[{\]}\\|;:'",<.>/?]/,
} as const;

const STAGES = {
  EMAIL: 1,
  PASSWORD: 2,
  OTP: 3,
} as const;

const API_ERROR_TYPES = {
  USER_INACTIVE: 'user_inactive',
  OTP_EXPIRED: 'otp_expired',
  RESEND_LIMITED: 'resend_limited',
  DEFAULT: 'default',
} as const;

type Stage = (typeof STAGES)[keyof typeof STAGES];
type ApiErrorType = (typeof API_ERROR_TYPES)[keyof typeof API_ERROR_TYPES];

interface Props {
  stage?: Stage;
  email?: string;
}

const props = defineProps<Props>();

const storeUser = useUserStore();
const storeSV = useSVStore();
const now = useNow();

const mutations = {
  updateToken: useUpdateSVTokenMutation(),
  sendActivate: useSVAuthSendActivateMutation(),
  checkCredential: useSVAuthCheckCredentialMutation(),
  create: useSVAuthCreateMutation(),
  activateOTP: useSVAuthActivateOTPMutation(),
};

const { svUserBalanceQuery } = useFetchQueries();
const { user } = storeToRefs(storeUser);
const { closeDialog, openDialog } = useMicroRoute();
const { t } = useI18n();
const { tracker } = usePageTracker();

const stage = ref<Stage>(props.stage ?? STAGES.EMAIL);
const error = shallowRef('');
const matched = shallowRef(false);
const resendAfter = shallowRef('');

const countdownResend = computed(() => {
  if (!resendAfter.value) return 0;
  return Math.max(0, +new Date(resendAfter.value) - now.value);
});

const loading = computed(
  () =>
    mutations.checkCredential.isPending.value ||
    mutations.create.isPending.value ||
    mutations.activateOTP.isPending.value,
);

const header = computed(() => {
  const headerMap = {
    [STAGES.EMAIL]: 'CREATE_KEE_HEADER_1',
    [STAGES.PASSWORD]: 'CREATE_KEE_HEADER_2',
    [STAGES.OTP]: 'CREATE_KEE_HEADER_3',
  } as const;
  return t(headerMap[stage.value]);
});

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;

  const errorHandlers = {
    [API_ERROR_TYPES.USER_INACTIVE]: () => {
      setTouched(false);
      stage.value = STAGES.OTP;
      setFieldValue('code', '');
      void sendActivationCode(values.email);
    },
    [API_ERROR_TYPES.OTP_EXPIRED]: () => {
      error.value = t('CREATE_KEE_OTP_EXPIRED_CREDENTIALS');
    },
    [API_ERROR_TYPES.RESEND_LIMITED]: () => {
      error.value = t('CREATE_KEE_RESEND_LIMIT_CREDENTIALS');
    },
    [API_ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ApiErrorType;
  const handler = errorHandlers[errorType] || errorHandlers[API_ERROR_TYPES.DEFAULT];

  handler();
}

function validatePassword(value: string): boolean {
  if (!value) return false;

  const requirements = {
    text_length: value.length >= 8,
    lowercase: PASSWORD_REGEX.LOWERCASE.test(value),
    uppercase: PASSWORD_REGEX.UPPERCASE.test(value),
    digit: PASSWORD_REGEX.DIGIT.test(value),
    special: PASSWORD_REGEX.SPECIAL.test(value),
  };

  const isValid = Object.values(requirements).every(Boolean);
  matched.value = isValid;
  return isValid;
}

const validationSchemas = computed(() => {
  const baseSchemas = {
    [STAGES.EMAIL]: () =>
      yup.object({
        email: yup.string().required(t('EMAIL_REQUIRED')).matches(EMAIL_REGEX, t('EMAIL_INVALID')),
      }),
    [STAGES.PASSWORD]: () =>
      yup.object({
        password: yup
          .string()
          .required(t('PASSWORD_REQUIRED'))
          .test('password', t('PASSWORD_INVALID'), validatePassword),
        cf_password: yup
          .string()
          .required(t('RE_ENTER_PASSWORD_REQUIRED'))
          .oneOf([yup.ref('password')], t('PASSWORD_NOT_MATCH')),
        tac: yup.boolean().oneOf([true], t('SIGNUP_TNC_ERROR')),
      }),
    [STAGES.OTP]: () =>
      yup.object({
        code: yup
          .string()
          .required(t('SIGNUP_FORM_OTP_REQUIRED'))
          .length(6, t('SIGNUP_FORM_OTP_INVALID')),
      }),
  };

  return baseSchemas[stage.value]();
});

const { handleSubmit, values, setTouched, setFieldError, setFieldValue } = useForm({
  initialValues: {
    email: props.email || '',
    password: '',
    cf_password: '',
    tac: false,
    code: '',
  },
  validationSchema: validationSchemas,
});

function createPayload(): SVCheckCredentialsPayload {
  if (!user.value) throw new Error('User not found');

  return {
    email: values.email,
    mobile_number: user.value.mobile_number,
    country: 'SG',
    sdk_linking: {
      hunter_id: user.value.hunter_id,
      user_id: String(user.value.id),
    },
  };
}

async function checkCredentials(): Promise<void> {
  try {
    const payload = createPayload();
    const data = await mutations.checkCredential.mutateAsync(payload);
    if (data.email) {
      setFieldError('email', t('CREATE_KEE_EMAIL_EXIST'));
    } else if (data.mobile_number && typeof data.mobile_number === 'string') {
      setFieldError('email', data.mobile_number);
    } else if (data.username || data.mobile_number) {
      setFieldError('email', t('CREATE_KEE_USER_NAME_EXIST'));
    } else {
      setTouched(false);
      stage.value = STAGES.PASSWORD;
    }
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

async function createAccount(): Promise<void> {
  try {
    const payload = createPayload();
    await mutations.create.mutateAsync({ ...payload, password: values.password });
    await sendActivationCode(values.email);
    setTouched(false);
    stage.value = STAGES.OTP;
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

async function activateAccount(): Promise<void> {
  try {
    const data = await mutations.activateOTP.mutateAsync({
      email: values.email,
      code: values.code,
    });
    storeSV.setToken(data.token);
    storeSV.setUser(data.user);

    await Promise.all([
      mutations.updateToken.mutateAsync(data.token),
      svUserBalanceQuery.refetch(),
    ]);

    closeDialog('sqkii_vouchers_create_account');
    successNotify({ message: t('CREATE_KEE_SUCCESS') });
    openDialog('sqkii_vouchers_set_pin');
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

async function sendActivationCode(email: string): Promise<void> {
  try {
    const data = await mutations.sendActivate.mutateAsync(email);
    resendAfter.value = data.resend_after;
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

const onSubmit = handleSubmit(async (): Promise<void> => {
  switch (stage.value) {
    case STAGES.EMAIL:
      await checkCredentials();
      break;
    case STAGES.PASSWORD:
      await createAccount();
      break;
    case STAGES.OTP:
      await activateAccount();
      break;
  }
  tracker({
    id: 'sqkii_vouchers_create_account',
    action: 'click',
    data: {
      target: 'submit',
      stage: Object.keys(STAGES)[stage.value - 1]?.toLocaleLowerCase(),
    },
  });
});

async function resendCode(): Promise<void> {
  await sendActivationCode(values.email);
  tracker({
    id: 'sqkii_vouchers_create_account',
    action: 'click',
    data: {
      target: 'resend_otp',
    },
  });
}

function clearError(): void {
  error.value = '';
}

watch(() => [values.email, values.password, values.cf_password, values.code], clearError);

async function handleClick(e: Event): Promise<void> {
  const target = e.target as HTMLElement;
  switch (target.id) {
    case 'CREATE_KEE_RESEND_OTP':
      await resendCode();
      break;
    case 'LINK_KEE':
      closeDialog('sqkii_vouchers_create_account');
      openDialog('sqkii_vouchers_link_account');
      tracker({
        id: 'sqkii_vouchers_create_account',
        action: 'click',
        data: {
          target: 'link_account',
        },
      });
      break;
    case 'TC':
      openDialog('tac', {
        onClose: () => setFieldValue('tac', true),
      });
      tracker({
        id: 'sqkii_vouchers_create_account',
        action: 'click',
        data: {
          target: 'tac',
        },
      });
      break;
    default:
      break;
  }
}

onMounted(async () => {
  await nextTick();

  if (props.email) await resendCode();

  addEventListener('click', (e) => {
    void handleClick(e);
  });
});

onBeforeUnmount(() => {
  removeEventListener('click', (e) => {
    void handleClick(e);
  });
});
</script>
<template>
  <Dialog>
    <template #btnTopLeft>
      <Button
        v-if="stage === STAGES.OTP"
        track-id="disable-track"
        shape="square"
        variant="secondary"
        @click="stage = STAGES.PASSWORD"
      >
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>

    <template #header>
      <div v-html="header" />
    </template>

    <q-form @submit.prevent="onSubmit" class="text-center">
      <!-- Email Stage -->
      <section v-show="stage === STAGES.EMAIL">
        <div class="text-sm mb-5" v-html="t('CREATE_KEE_DESC_1')" />
        <VeeInput
          name="email"
          :label="t('LINK_KEE_LABEL_EMAIL')"
          autofocus
          class="mb-5"
          :error="!!error"
        />
        <div v-if="error" class="card-error mt-2 mb-5 text-center" v-html="t(error)" />
      </section>

      <!-- Password Stage -->
      <section v-show="stage === STAGES.PASSWORD">
        <div class="text-sm mb-5" v-html="t('CREATE_KEE_DESC_2')" />

        <VeeInput class="mb-5" name="password" :label="t('PASSWORD')" type="password" />

        <Requirements
          v-if="values.password && !matched"
          class="mb-3 -mt-4"
          :password="values.password"
          @valid="matched = $event"
        />

        <VeeInput class="mb-5" name="cf_password" :label="t('RE_ENTER_PASSWORD')" type="password" />

        <VeeCheckBox
          class="mb-1 text-left"
          name="tac"
          :label="t('SIGNUP_TNC_CHECKBOX')"
          @click="handleClick"
        />
      </section>

      <!-- OTP Stage -->
      <section v-show="stage === STAGES.OTP">
        <div class="text-sm" v-html="t('CREATE_KEE_DESC_3')" />
        <div class="text-lg font-bold">{{ values.email }}</div>
        <div class="text-sm mb-5" v-html="t('CREATE_KEE_DESC_4')" />

        <VeeOTP class="mb-5" name="code" :num-inputs="6" :error="!!error" />

        <div v-if="error" class="card-error mt-2 mb-5 text-center" v-html="t(error)" />

        <!-- Resend Section -->
        <template v-if="resendAfter">
          <div
            class="text-sm mb-5"
            v-html="
              t('CREATE_KEE_DESC_5', {
                TIME: dateTimeFormat(resendAfter, FULL_DATE_TIME_12H_FORMAT_IN_SECOND),
                TIMEZONE: dayjs(Date.now()).format('Z').replace(':00', ''),
              })
            "
          />
          <div class="text-sm mb-5">
            <span
              v-show="countdownResend > 0"
              v-html="
                t('CREATE_KEE_DESC_6', {
                  TIME: timeCountDown(countdownResend),
                })
              "
            />
            <span
              :class="{ 'opacity-0 absolute': countdownResend > 0 }"
              v-html="t('CREATE_KEE_DESC_7')"
            />
          </div>
        </template>
      </section>

      <Button
        track-id="disable-track"
        :label="t('CREATE_KEE_BTN_NEXT')"
        class="!w-[210px] !mb-5"
        :loading="loading"
        type="submit"
      />

      <div v-if="stage === STAGES.EMAIL" class="text-sm" v-html="t('CREATE_KEE_DESC_8')" />
    </q-form>
  </Dialog>
</template>
