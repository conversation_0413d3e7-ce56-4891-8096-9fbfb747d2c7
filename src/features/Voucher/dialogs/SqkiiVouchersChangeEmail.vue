<script lang="ts" setup>
import {
  dateTimeFormat,
  EMAIL_REGEX,
  beautifyPhoneNumber,
  FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
  timeCountDown,
} from '@utils';
import { useForm } from 'vee-validate';
import dayjs from 'dayjs';
import { useFetchQueries, useNow, usePageTracker } from '@composables';
import { useSVStore } from '@stores';
import { useSVAuthChangeMailMutation, useSVAuthRequestChangeMailMutation } from '@services';
import { APISVResponseError } from '@types';
import * as yup from 'yup';

const FORM_STEPS = {
  OTP: 1,
  EMAIL: 2,
  SUCCESS: 3,
} as const;

const OTP_LENGTH = 6;

const ERROR_TYPES = {
  RESEND_LIMITED: 'resend_limited',
  EXIST_EMAIL: 'exist_email',
  OTP_EXPIRED: 'otp_expired',
  VERIFY_SUCCESS: 'verify_success',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

type FormStep = (typeof FORM_STEPS)[keyof typeof FORM_STEPS];

interface FormValues {
  otp_code: string;
  email: string;
}

const now = useNow();
const storeSV = useSVStore();
const changeMailMutation = useSVAuthChangeMailMutation();
const requestChangeMailMutation = useSVAuthRequestChangeMailMutation();

const { svUser } = storeToRefs(storeSV);
const { t } = useI18n();
const { tracker } = usePageTracker();
const { svUserQuery } = useFetchQueries();

const stage = ref<FormStep>(FORM_STEPS.OTP);
const error = ref('');
const resendAfter = ref('');

const loading = computed(() => changeMailMutation.isPending.value);

const countdownResend = computed(() => {
  if (!resendAfter.value) return 0;
  return Math.max(0, +new Date(resendAfter.value) - now.value);
});

const isResendAvailable = computed(() => countdownResend.value <= 0);

const header = computed(() => {
  const headerMap: Record<FormStep, string> = {
    [FORM_STEPS.OTP]: t('CHANGE_MAIL_HEADER_1'),
    [FORM_STEPS.EMAIL]: t('CHANGE_MAIL_HEADER_2'),
    [FORM_STEPS.SUCCESS]: t('CHANGE_MAIL_HEADER_3'),
  };
  return headerMap[stage.value];
});

const validationSchema = computed(() => {
  const schemaMap = {
    [FORM_STEPS.OTP]: {
      otp_code: yup
        .string()
        .required(t('SIGNUP_FORM_OTP_REQUIRED'))
        .length(OTP_LENGTH, t('SIGNUP_FORM_OTP_INVALID')),
    },
    [FORM_STEPS.EMAIL]: {
      email: yup
        .string()
        .required(t('EMAIL_REQUIRED'))
        .matches(EMAIL_REGEX, t('EMAIL_INVALID'))
        .test('unique', t('EMAIL_SAME_CURRENT_MAIL'), (value) => {
          return svUser.value?.email !== value;
        }),
    },
    [FORM_STEPS.SUCCESS]: {},
  };
  return yup.object().shape(schemaMap[stage.value]);
});

const { handleSubmit, values, setTouched } = useForm<FormValues>({
  initialValues: {
    otp_code: '',
    email: '',
  },
  validationSchema,
});

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;
  const errorHandlers = {
    [ERROR_TYPES.RESEND_LIMITED]: () => {
      error.value = t('CHANGE_MAIL_RESEND_LIMIT_CREDENTIALS');
    },
    [ERROR_TYPES.EXIST_EMAIL]: () => {
      error.value = t('CHANGE_MAIL_EXIST_EMAIL');
    },
    [ERROR_TYPES.OTP_EXPIRED]: () => {
      error.value = t('CHANGE_MAIL_INVALID_OTP');
    },
    [ERROR_TYPES.VERIFY_SUCCESS]: () => {
      setTouched(false);
      stage.value = FORM_STEPS.EMAIL;
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = data?.verify
    ? errorHandlers[ERROR_TYPES.VERIFY_SUCCESS]
    : errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

function clearError(): void {
  error.value = '';
}

const onSubmit = handleSubmit(async (values: FormValues): Promise<void> => {
  try {
    await changeMailMutation.mutateAsync({
      otp_code: values.otp_code,
      email: stage.value === FORM_STEPS.OTP ? undefined : values.email,
    });
    if (stage.value === FORM_STEPS.OTP) stage.value = FORM_STEPS.EMAIL;
    else {
      stage.value = FORM_STEPS.SUCCESS;
      await svUserQuery.refetch();
    }
    tracker({
      id: 'sqkii_vouchers_change_email',
      action: 'click',
      data: {
        target: 'submit',
        step: Object.keys(FORM_STEPS)[stage.value - 1]?.toLowerCase(),
      },
    });
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
});

async function requestOTP(): Promise<void> {
  try {
    if (!svUser.value?.mobile_number) throw new Error('Mobile number not available');

    const data = await requestChangeMailMutation.mutateAsync(svUser.value.mobile_number);
    tracker({
      id: 'sqkii_vouchers_change_email',
      action: 'click',
      data: {
        target: 'request_otp',
      },
    });
    resendAfter.value = data.resend_after;
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

function handleBack(): void {
  tracker({
    id: 'sqkii_vouchers_change_email',
    action: 'click',
    data: {
      target: 'back',
      step: Object.keys(FORM_STEPS)[stage.value - 1]?.toLowerCase(),
    },
  });
  stage.value = FORM_STEPS.OTP;
  setTouched(false);
  clearError();
}

async function handleResendOTP(): Promise<void> {
  if (!isResendAvailable.value) return;
  clearError();
  await requestOTP();
}

async function handleClick(e: Event): Promise<void> {
  const target = e.target as HTMLElement;
  if (target.id === 'CHANGE_MAIL_RESEND_OTP') {
    await handleResendOTP();
  }
}

watch(() => [values.otp_code, values.email], clearError);

onMounted(async () => {
  await nextTick();
  await requestOTP();

  addEventListener('click', (e) => {
    void handleClick(e);
  });
});

onBeforeUnmount(() => {
  removeEventListener('click', (e) => {
    void handleClick(e);
  });
});
</script>
<template>
  <Dialog>
    <template #btnTopLeft>
      <Button
        v-if="stage === FORM_STEPS.EMAIL"
        track-id="disable-track"
        shape="square"
        variant="secondary"
        @click="handleBack"
      >
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>

    <template #header>
      <div v-html="header" />
    </template>

    <q-form @submit.prevent="onSubmit" class="text-center">
      <!-- OTP Step -->
      <section v-show="stage === FORM_STEPS.OTP">
        <div class="text-sm" v-html="t('CHANGE_MAIL_DESC_1')" />

        <div v-if="svUser" class="text-lg font-bold">
          {{ beautifyPhoneNumber(svUser.mobile_number) }}
        </div>

        <div class="text-sm mb-5" v-html="t('CHANGE_MAIL_DESC_2')" />

        <VeeOTP class="mb-5" name="otp_code" :num-inputs="OTP_LENGTH" :error="!!error" />

        <div v-if="error" class="card-error mt-2 mb-5 text-center" v-html="t(error)" />

        <!-- Resend Section -->
        <template v-if="resendAfter">
          <div
            class="text-sm mb-5"
            v-html="
              t('CHANGE_MAIL_DESC_3', {
                TIME: dateTimeFormat(resendAfter, FULL_DATE_TIME_12H_FORMAT_IN_SECOND),
                TIMEZONE: dayjs().format('Z').replace(':00', ''),
              })
            "
          />

          <div class="text-sm mb-5">
            <span
              v-show="!isResendAvailable"
              v-html="
                t('CHANGE_MAIL_DESC_4', {
                  TIME: timeCountDown(countdownResend),
                })
              "
            />
            <div v-show="isResendAvailable" v-html="t('CHANGE_MAIL_DESC_5')" />
          </div>
        </template>
      </section>

      <!-- Email Step -->
      <section v-show="stage === FORM_STEPS.EMAIL">
        <div class="text-sm mb-5" v-html="t('CHANGE_MAIL_DESC_6')" />

        <VeeInput
          name="email"
          :label="t('LINK_KEE_LABEL_EMAIL')"
          autofocus
          class="mb-5"
          :error="!!error"
        />

        <div v-if="error" class="card-error mt-2 mb-5 text-center" v-html="t(error)" />
      </section>

      <!-- Success Step -->
      <section v-show="stage === FORM_STEPS.SUCCESS">
        <div class="flex flex-col justify-center items-center">
          <Icon class="mb-5" name="top-up-success" :size="140" />
          <div class="text-sm" v-html="t('CHANGE_MAIL_DESC_7')" />
        </div>
      </section>

      <!-- Submit Button -->
      <Button
        v-if="stage !== FORM_STEPS.SUCCESS"
        track-id="disable-track"
        :label="t('CHANGE_MAIL_BTN_NEXT')"
        :loading="loading"
        type="submit"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
