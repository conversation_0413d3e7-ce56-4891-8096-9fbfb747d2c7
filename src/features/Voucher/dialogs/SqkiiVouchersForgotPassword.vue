<script lang="ts" setup>
import { useForm } from 'vee-validate';
import { EMAIL_REGEX, timeCountDown } from '@utils';
import { useNow, usePageTracker } from '@composables';
import { useSVAuthForgotPasswordMutation } from '@services';
import { APISVResponseError } from '@types';
import * as yup from 'yup';

const FORM_STEPS = {
  EMAIL: 1,
  SUCCESS: 2,
} as const;

type FormStep = (typeof FORM_STEPS)[keyof typeof FORM_STEPS];

const ERROR_TYPES = {
  NO_EMAIL: 'no_email',
  TEMP_LOCKED: 'temp_locked',
  RESEND_LIMITED: 'resend_limited',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

const now = useNow();
const forgotPasswordMutation = useSVAuthForgotPasswordMutation();

const { t } = useI18n();
const { closeDialog } = useMicroRoute();
const { tracker } = usePageTracker();

const stage = ref<FormStep>(FORM_STEPS.EMAIL);
const error = ref('');
const locked_until = ref('');

const loading = computed(() => forgotPasswordMutation.isPending.value);

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return +new Date(locked_until.value) - now.value;
});

const isCountdownActive = computed(() => countdown.value > 0);

const headerText = computed(() => {
  const headerMap: Record<FormStep, string> = {
    [FORM_STEPS.EMAIL]: t('KEE_FORGOT_PW_HEADER'),
    [FORM_STEPS.SUCCESS]: t('KEE_FORGOT_PW_HEADER_CHECK_MAIL'),
  };
  return headerMap[stage.value];
});

const submitButtonLabel = computed(() => {
  if (isCountdownActive.value) return timeCountDown(countdown.value);
  return t('KEE_FORGOT_PW_BTN_SUBMIT');
});

const validationSchema = yup.object({
  email: yup.string().required(t('EMAIL_REQUIRED')).matches(EMAIL_REGEX, t('EMAIL_INVALID')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    email: '',
  },
  validationSchema,
});

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;

  const errorHandlers = {
    [ERROR_TYPES.NO_EMAIL]: () => {
      error.value = t('LINK_KEE_INVALID_CREDENTIALS', {
        URL: process.env.APP_SV_URL,
      });
    },
    [ERROR_TYPES.TEMP_LOCKED]: () => {
      error.value = t('LINK_KEE_MAX_CREDENTIALS');
      locked_until.value = data?.locked_until || '';
    },
    [ERROR_TYPES.RESEND_LIMITED]: () => {
      error.value = t('KEE_RESEND_LIMIT_CREDENTIALS', {
        URL: process.env.APP_SV_URL,
      });
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

const onSubmit = handleSubmit(async (values): Promise<void> => {
  try {
    await forgotPasswordMutation.mutateAsync(values.email);
    stage.value = FORM_STEPS.SUCCESS;
    tracker({
      id: 'sqkii_vouchers_forgot_password',
      action: 'click',
      data: {
        target: 'send_email_success',
      },
    });
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
});

function onClose(): void {
  if (loading.value) return;
  closeDialog('sqkii_vouchers_forgot_password');
}

function clearError(): void {
  error.value = '';
}

watch(() => [values.email], clearError);
</script>
<template>
  <Dialog @close="onClose">
    <template #header>
      <div v-html="headerText"></div>
    </template>
    <template v-if="stage === FORM_STEPS.EMAIL">
      <q-form @submit="onSubmit" class="text-center">
        <div class="text-sm px-7 mb-5" v-html="t('KEE_FORGOT_PW_DESC')"></div>
        <VeeInput class="mb-5" name="email" :label="t('LINK_KEE_LABEL_EMAIL')" :error="!!error" />
        <div class="card-error mt-2 mb-5 text-center" v-if="!!error" v-html="t(error)"></div>
        <Button
          track-id="disable-track"
          class="!w-[210px] mb-5"
          :loading="loading"
          :disable="isCountdownActive"
          :label="submitButtonLabel"
          type="submit"
        />
      </q-form>
    </template>
    <template v-if="stage === FORM_STEPS.SUCCESS">
      <div class="text-sm text-center" v-html="t('KEE_FORGOT_PW_DESC_1')"></div>
    </template>
  </Dialog>
</template>
