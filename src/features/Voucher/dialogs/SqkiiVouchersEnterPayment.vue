<script lang="ts" setup>
import { useForm } from 'vee-validate';
import { APISVResponseError } from '@types';
import { useSVPaymentScanMutation } from '@services';
import * as yup from 'yup';
import { usePageTracker } from '@composables';

const ERROR_TYPES = {
  OUTLET_INVALID: 'outlet_invalid',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

const mutation = useSVPaymentScanMutation();

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
const { tracker } = usePageTracker();

const error = ref('');

const loading = computed(() => mutation.isPending.value);

const validationSchema = yup.object().shape({
  code: yup.string().required(t('CODE_REQUIRED')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    code: '',
  },
  validationSchema,
});

const onSubmit = handleSubmit(async (values): Promise<void> => {
  try {
    const data = await mutation.mutateAsync(values.code);
    openDialog('sqkii_vouchers_pay_to_merchant', {
      outlet: data,
    });
    tracker({
      id: 'sqkii_vouchers_enter_payment',
      action: 'click',
      data: {
        target: 'enter_payment_code_success',
      },
    });
  } catch (err) {
    handleApiError(err as APISVResponseError);
  }
});

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;
  const errorHandlers = {
    [ERROR_TYPES.OUTLET_INVALID]: () => {
      error.value = t('OUTLET_INVALID_CODE');
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

function clearError(): void {
  error.value = '';
}

watch(() => values.code, clearError);
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('ENTER_PAYMENT_CODE_HEADER')"></div>
    </template>
    <q-form @submit="onSubmit" class="px-5 text-center">
      <VeeInput class="mb-5" name="code" :error="!!error" autofocus>
        <template #prepend>
          <div class="border-r w-8 ml-[6px] h-[calc(100%-18px)]">
            <Icon
              name="icons/ic_scan"
              :size="20"
              style="opacity: 0.7"
              @click="closeDialog('sqkii_vouchers_enter_payment')"
            />
          </div>
        </template>
      </VeeInput>
      <div class="card-error mt-2 mb-5 text-center" v-if="!!error" v-html="t(error)"></div>
      <Button
        track-id="disable-track"
        :loading="loading"
        type="submit"
        :label="t('ENTER_PAYMENT_CODE_BTN_SUBMIT')"
      />
    </q-form>
  </Dialog>
</template>
