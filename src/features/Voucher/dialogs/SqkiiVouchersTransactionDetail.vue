<script lang="ts" setup>
import {
  SqkiiVouchersTransactionStatus,
  SqkiiVouchersTransactionType,
  SqkiiVouchersTransactionTypeLabels,
} from '@enums';
import { numeralFormat } from '@utils';
import { useBAStore } from '@stores';
import { SVTransactionHistory } from '@types';

interface Props {
  transaction: SVTransactionHistory;
}

interface StatusColorMap {
  readonly [SqkiiVouchersTransactionStatus.COMPLETED]: string;
  readonly [SqkiiVouchersTransactionStatus.FAILED]: string;
  readonly [SqkiiVouchersTransactionStatus.REFUNDED]: string;
}

const STATUS_COLORS: StatusColorMap = {
  [SqkiiVouchersTransactionStatus.COMPLETED]: 'text-[#53E56B]',
  [SqkiiVouchersTransactionStatus.FAILED]: 'text-[#FF4D4D]',
  [SqkiiVouchersTransactionStatus.REFUNDED]: 'text-[#BC18D7]',
} as const;

const props = defineProps<Props>();

const storeBA = useBAStore();

const { t } = useI18n();
const { user_brand_actions } = storeToRefs(storeBA);

const boundUserBA = computed(() => {
  return user_brand_actions.value.find((uba) => uba?.data?.tracking_id === props.transaction.id);
});

const transactionTypeLabel = computed((): string => {
  return SqkiiVouchersTransactionTypeLabels[props.transaction.type] ?? 'Unknown';
});

const statusColorClass = computed((): string => {
  return STATUS_COLORS[props.transaction.status] ?? '';
});

const formattedAmount = computed((): string => {
  const { currency, amount } = props.transaction;
  return `${currency}$ ${numeralFormat(amount, '0,0.00')}`;
});

const transactionId = computed((): string => {
  return props.transaction.type === SqkiiVouchersTransactionType.PAYMENT
    ? props.transaction.payment_id
    : props.transaction.txn_id.toString();
});

const finalCrystals = computed((): number => {
  const brandActionCrystals = boundUserBA.value?.metadata?.total;
  const transactionCrystals = props.transaction?.rewarded?.crystals;
  return brandActionCrystals ?? transactionCrystals ?? 0;
});

const formattedCrystals = computed((): string => {
  return numeralFormat(finalCrystals.value);
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('TRANSACTION_DETAILS_HEADER')" />
    </template>

    <div class="flex flex-col gap-5 text-center">
      <!-- Transaction Type -->
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_TYPE')" />
        <div>{{ transactionTypeLabel }}</div>
      </div>

      <!-- Transaction Status -->
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_STATUS')" />
        <div class="font-bold uppercase" :class="statusColorClass">
          {{ transaction.status }}
        </div>
      </div>

      <!-- Merchant Name -->
      <div v-if="transaction.outlet_name" class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_MERCHANT')" />
        <div>{{ transaction.outlet_name }}</div>
      </div>

      <!-- Transaction Amount -->
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_AMOUNT')" />
        <div>{{ formattedAmount }}</div>
      </div>

      <!-- Crystals Earned -->
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_CRYSTALS')" />
        <div class="flex items-center justify-center gap-1">
          <span class="text-sm">{{ formattedCrystals }}</span>
          <Icon name="crystal-s" />
        </div>
      </div>

      <!-- Transaction ID -->
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_ID')" />
        <div class="font-mono text-sm break-all">{{ transactionId }}</div>
      </div>

      <!-- Contact Information -->
      <div class="px-5 text-sm opacity-75" v-html="t('TRANSACTION_DETAILS_CONTACT')" />
    </div>
  </Dialog>
</template>
