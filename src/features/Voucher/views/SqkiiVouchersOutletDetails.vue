<script lang="ts" setup>
import { SVOutlet } from '@types';

interface Props {
  merchant: string;
  outlet: SVOutlet;
}

const props = defineProps<Props>();

const { t } = useI18n();
const { push } = useMicroRoute();

const OPENING_HOURS_KEYS = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
  'weekday',
  'weekend',
  'public_holiday',
  'eve_holiday',
] as const;

type OpeningHoursKey = (typeof OPENING_HOURS_KEYS)[number];

const openingHours = computed(() => {
  return OPENING_HOURS_KEYS.filter((key): key is OpeningHoursKey => {
    const value = props.outlet[key];
    return Boolean(value && value.trim());
  }).map((key) => ({
    key,
    label: t(key),
    value: props.outlet[key],
  }));
});
</script>
<template>
  <div class="fullscreen bg-[#090422] flex flex-nowrap flex-col overflow-hidden">
    <!-- Header Section -->
    <div class="relative flex justify-center items-center w-full h-16 px-20 mb-5 shrink-0">
      <Button
        track-id="disable-track"
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-extrabold text-center w-full">{{ merchant }}</div>
    </div>

    <!-- Content Section -->
    <div class="overflow-y-auto px-6 pb-5">
      <!-- Outlet Name -->
      <div class="text-2xl font-bold mb-6">{{ outlet.name }}</div>

      <!-- Address Section -->
      <div class="mb-6">
        <div class="text-xl font-bold mb-2">{{ t('OUTLET_DETAILS_ADDRESS') }}</div>
        <div class="text-sm not-italic">{{ outlet.address }}</div>
      </div>

      <!-- Opening Hours Section -->
      <div class="mb-10">
        <div class="text-xl font-bold mb-3">{{ t('OUTLET_DETAILS_OPENING') }}</div>
        <div class="flex flex-col gap-2">
          <div v-for="hours in openingHours" :key="hours.key" class="flex flex-col">
            <div class="text-xs text-[#BCBCBC] mb-1">{{ hours.label }}</div>
            <div class="text-sm font-medium">{{ hours.value }}</div>
          </div>
        </div>
      </div>

      <!-- Contact Section -->
      <div class="text-center">
        <div class="text-sm px-5" v-html="t('OUTLET_DETAILS_CONTACT')"></div>
      </div>
    </div>
  </div>
</template>
