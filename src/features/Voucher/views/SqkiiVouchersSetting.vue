<script lang="ts" setup>
import { usePageTracker } from '@composables';
import { successNotify } from '@utils';
import { useSVStore } from '@stores';
import { useSVAuthUnlinkMutation, useUpdateSVTokenMutation } from '@services';
import { STORAGE_KEYS } from '@enums';

const storeSV = useSVStore();
const unlinkMutation = useSVAuthUnlinkMutation();
const updateSVTokenMutation = useUpdateSVTokenMutation();

const { svUser } = storeToRefs(storeSV);
const { t } = useI18n();
const { push, openDialog } = useMicroRoute();
const { tracker } = usePageTracker();

function goToDialog(path: string): void {
  openDialog(path);
  tracker({
    id: 'sqkii_vouchers_setting',
    action: 'click',
    data: {
      target: path,
    },
  });
}

async function onSubmit(): Promise<void> {
  try {
    await unlinkMutation.mutateAsync();
    await updateSVTokenMutation.mutateAsync('');
    LocalStorage.remove(STORAGE_KEYS.SV_TOKEN);
    LocalStorage.remove(STORAGE_KEYS.SV_CLOSED_CALLOUT);
    LocalStorage.remove(STORAGE_KEYS.SV_VISITED);
    LocalStorage.remove(STORAGE_KEYS.SV_LAST_TYPE);
    storeSV.$reset();
    successNotify({
      message: t('SQKII_VOUCHERS_UNLINK_SUCCESS'),
    });
    push('home');
    tracker({
      id: 'sqkii_vouchers_setting',
      action: 'click',
      data: {
        target: 'unlink_success',
      },
    });
  } catch (error) {
    console.error('Error unlinking SV account:', error);
  }
}
</script>
<template>
  <div class="fullscreen sqkii-vouchers-setting setting-sg">
    <div class="fixed bg-black opacity-50 w-full h-full pointer-events-none"></div>
    <div class="fixed top-0 left-0 z-10 flex justify-center items-center h-20 w-full">
      <Button
        track-id="disable-track"
        class="absolute left-3"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-extrabold" v-html="t('SQKII_VOUCHERS_SETTING_HEADING')"></div>
    </div>
    <div class="relative w-full h-[calc(100%-150px)] overflow-y-auto mt-[150px] px-5 pb-5">
      <div class="text-center flex flex-col gap-2 mb-5" v-if="svUser?.email">
        <div class="text-xs opacity-70" v-html="t('SQKII_VOUCHERS_SETTING_DESC')"></div>
        <div class="text-base font-bold">{{ svUser?.email }}</div>
      </div>
      <div class="flex flex-col gap-5 justify-center items-center mb-10">
        <Button
          track-id="disable-track"
          class="!w-[250px]"
          :disable="!!svUser?.email"
          @click="goToDialog('sqkii_vouchers_link_account')"
        >
          <div class="flex items-center flex-nowrap gap-2">
            <div v-html="t('SQKII_VOUCHERS_SETTING_BTN_SYNC')"></div>
            <Icon v-if="!!svUser?.email" name="ticked" :size="20" />
          </div>
        </Button>
        <Button
          track-id="disable-track"
          variant="purple"
          class="!w-[250px]"
          :label="t('SQKII_VOUCHERS_SETTING_BTN_CHANGE_PIN')"
          :disable="!svUser?.email"
          @click="goToDialog('sqkii_vouchers_change_pin')"
        />
        <Button
          track-id="disable-track"
          variant="purple"
          class="!w-[250px]"
          :label="t('SQKII_VOUCHERS_SETTING_BTN_CHANGE_EMAIL')"
          :disable="!svUser?.email"
          @click="goToDialog('sqkii_vouchers_change_email')"
        />
        <Button
          track-id="disable-track"
          variant="purple"
          class="!w-[250px]"
          :label="t('SQKII_VOUCHERS_UNSYNC')"
          v-if="!!svUser?.email"
          :loading="unlinkMutation.isPending.value"
          @click="onSubmit"
        />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.sqkii-vouchers-setting {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &.setting-sg {
    background: url('/imgs/bg-menu-sg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .language {
    width: 70%;
    background: #04081d;
    border-radius: 10px;
    box-shadow: 2px 2px 10px rgba(#04081d, 0.1);
    border: 1px solid #00e0ff;
  }
}
</style>
