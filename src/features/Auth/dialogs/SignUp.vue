<script lang="ts" setup>
import { useNow, usePageTracker } from '@composables';
import { useUserStore } from '@stores';
import { useForm } from 'vee-validate';
import {
  beautifyPhoneNumber,
  dateTimeFormat,
  FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
  timeCountDown,
} from '@utils';
import { TurnstileCaptcha } from '../components';
import { omitBy, isEmpty } from 'lodash';
import { APIResponseError, CountryRegion, User, ResendOTP, ResendOTPPayload } from '@types';
import { useUserResendOTPMutation, useUserSignUpMutation } from '@services';
import * as yup from 'yup';
import dayjs from 'dayjs';

const PASSWORD_REQUIREMENTS = {
  MIN_LENGTH: 8,
  REGEX_DIGIT: /\d/,
  REGEX_LOWERCASE: /[a-z]/,
  REGEX_UPPERCASE: /[A-Z]/,
  REGEX_SPECIAL: /[`~!@#$%^&*()\-_+=[{\]}\\|;:'",<.>/?]/,
} as const;

const OTP_CONFIG = {
  LENGTH: 6,
  EXPIRY_MINUTES: 3,
  MAX_ATTEMPTS: 4,
} as const;

const CUSTOM_COUNTRIES: CountryRegion[] = [
  {
    name: 'Singapore',
    code: '+65',
    iso: 'SG',
    flag: 'https://cdn.kcak11.com/CountryFlags/countries/sg.svg',
    mask: ['#### ####'],
    currency: 'SGD',
    currencyName: 'Singapore Dollar (SGD)',
    url: 'https://huntthemouse.sqkii.com',
  },
];

const FORM_STEPS = {
  MOBILE_NUMBER: 1,
  OTP: 2,
  PASSWORD: 3,
} as const;

type FormStep = (typeof FORM_STEPS)[keyof typeof FORM_STEPS];

const ERROR_TYPES = {
  INVALID_PASSWORD: 'invalid_password',
  INVALID_OTP: 'invalid_otp',
  MOBILE_NUMBER_EXISTED: 'mobile_number_existed',
  INVALID_VALUE: 'Invalid value',
  OTP_LOCKED: 'otp_locked',
  DEFAULT: 'default',
  BANNED_NUMBER: 'banned_number',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

interface FormData {
  mobile_number: string;
  otp: string;
  password: string;
  cf_password: string;
  next_otp_at: string;
  expire_at: string;
  captcha_token: string;
  tac: boolean;
}

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const mutationSignUp = useUserSignUpMutation();
const mutationResendOTP = useUserResendOTPMutation();
const now = useNow();

const { tracker, trackWithLocation } = usePageTracker();
const { openDialog, activePage, push } = useMicroRoute();
const { t } = useI18n();
const { isTestingEnv, turnstileDumyToken } = storeToRefs(storeUser);

const step = ref<FormStep>(FORM_STEPS.MOBILE_NUMBER);
const country = ref<CountryRegion>();
const matched = ref(false);
const captchaRef = ref<InstanceType<typeof TurnstileCaptcha> | null>(null);

const validationSchemas = {
  [FORM_STEPS.MOBILE_NUMBER]: yup.object({
    mobile_number: yup.string().required(t('MOBILE_NUMBER_REQUIRED')),
  }),
  [FORM_STEPS.OTP]: yup.object({
    otp: yup
      .string()
      .required(t('SIGNUP_FORM_OTP_REQUIRED'))
      .length(OTP_CONFIG.LENGTH, t('SIGNUP_FORM_OTP_INVALID')),
  }),
  [FORM_STEPS.PASSWORD]: yup.object({
    password: yup
      .string()
      .required(t('PASSWORD_REQUIRED'))
      .test('password-strength', t('PASSWORD_INVALID'), validatePasswordStrength),
    cf_password: yup
      .string()
      .required(t('RE_ENTER_PASSWORD_REQUIRED'))
      .oneOf([yup.ref('password')], t('PASSWORD_NOT_MATCH')),
    tac: yup.boolean().oneOf([true], t('SIGNUP_TNC_ERROR')),
  }),
} as const;

const validationSchema = computed(() => validationSchemas[step.value]);

const initialValues: FormData = {
  mobile_number: '',
  otp: '',
  password: '',
  cf_password: '',
  next_otp_at: '',
  expire_at: '',
  captcha_token: '',
  tac: false,
};

const { handleSubmit, values, setFieldError, setTouched, setFieldValue } = useForm({
  initialValues,
  validationSchema,
});

function validatePasswordStrength(value: string): boolean {
  const requirements = {
    text_length: value.length >= PASSWORD_REQUIREMENTS.MIN_LENGTH,
    lowercase: PASSWORD_REQUIREMENTS.REGEX_LOWERCASE.test(value),
    uppercase: PASSWORD_REQUIREMENTS.REGEX_UPPERCASE.test(value),
    digit: PASSWORD_REQUIREMENTS.REGEX_DIGIT.test(value),
    special: PASSWORD_REQUIREMENTS.REGEX_SPECIAL.test(value),
  };

  matched.value = Object.values(requirements).every(Boolean);
  return matched.value;
}

const onSubmit = handleSubmit((formValues): void => {
  const mobile_number = formatMobileNumber(formValues.mobile_number);
  const payload = omitBy(
    {
      mobile_number,
      otp: formValues.otp,
      password: formValues.password,
      captcha_token: isTestingEnv ? turnstileDumyToken.value : formValues.captcha_token,
    },
    (value) => value === '' || value === undefined || isEmpty(value),
  );

  mutationSignUp.mutate(payload, {
    onSuccess: handleSubmissionSuccess,
    onError: handleSubmissionError,
  });
});

function formatMobileNumber(mobileNumber: string): string {
  return `${country.value?.code}${mobileNumber}`.replace(/\+/g, '');
}

function handleSubmissionSuccess(data: User): void {
  if (!data) return;

  if (data.expire_at) setFieldValue('expire_at', data.expire_at);

  if (data.next_otp_at) {
    setFieldValue('next_otp_at', data.next_otp_at);
    step.value = FORM_STEPS.OTP;
    setTouched(false);
    return;
  }

  storeUser.setUser(data);
  trackWithLocation({
    id: 'sign_up',
    action: 'click',
    data: { target: 'account_created' },
  });

  if (activePage.value === 'home') push('/home');
  emits('close');
  openDialog('signup_success', { user: data });
}

function handleSubmissionError(err: APIResponseError): void {
  const { data, error_message } = err;

  const errorHandlers = {
    [ERROR_TYPES.INVALID_PASSWORD]: () => {
      step.value = FORM_STEPS.PASSWORD;
      setTouched(false);
    },
    [ERROR_TYPES.INVALID_OTP]: () => {
      const errorMsg =
        data?.attempts === OTP_CONFIG.MAX_ATTEMPTS ? t('OTP_LIMIT_4') : t('INVALID_OTP');
      setFieldError('otp', errorMsg);
    },
    [ERROR_TYPES.MOBILE_NUMBER_EXISTED]: () => {
      step.value = FORM_STEPS.OTP;
      setTouched(false);
    },
    [ERROR_TYPES.INVALID_VALUE]: () => {
      step.value = FORM_STEPS.OTP;
      setTouched(false);
    },
    [ERROR_TYPES.OTP_LOCKED]: () => setFieldError('otp', t('OTP_LOCKED')),
    [ERROR_TYPES.BANNED_NUMBER]: () => {
      openDialog('locked_account', {
        lock_until: data?.info?.lock_until,
        type: 'banned',
      });
    },
    [ERROR_TYPES.DEFAULT]: () => setFieldError('mobile_number', t(error_message)),
  };

  const handler = errorHandlers[error_message as ErrorType] || errorHandlers[ERROR_TYPES.DEFAULT];
  handler();

  handlResetCapcha();
}

function resendOTP(): void {
  const mobile_number = formatMobileNumber(values.mobile_number);
  const payload: ResendOTPPayload = {
    mobile_number,
    type: 'register',
    captcha_token: isTestingEnv.value ? turnstileDumyToken.value : values.captcha_token,
  };

  mutationResendOTP.mutate(payload, {
    onSuccess: handleResendOTPSuccess,
    onError: handleSubmissionError,
  });
}

function handleResendOTPSuccess(data: ResendOTP): void {
  if (!data) return;

  setFieldValue('next_otp_at', data.next_otp_at);
  setFieldValue(
    'expire_at',
    data.expire_at || dayjs().add(OTP_CONFIG.EXPIRY_MINUTES, 'minute').toDate().toISOString(),
  );
}

function trackStepAction(target: string): void {
  tracker({
    id: 'sign_up',
    action: 'click',
    data: {
      target,
      step: Object.keys(FORM_STEPS)[step.value - 1]?.toLowerCase(),
    },
  });
}

function handleBack(): void {
  const stepActions = {
    [FORM_STEPS.OTP]: () => {
      setFieldValue('otp', '');
      step.value = FORM_STEPS.MOBILE_NUMBER;
    },
    [FORM_STEPS.PASSWORD]: () => {
      setFieldValue('password', '');
      setFieldValue('cf_password', '');
      step.value = FORM_STEPS.OTP;
    },
    default: () => {
      emits('close');
    },
  };

  const action = stepActions[step.value as keyof typeof stepActions] || stepActions.default;
  if (step.value !== FORM_STEPS.MOBILE_NUMBER) trackStepAction('back');
  else trackStepAction('close');
  action();
}

function handleCaptchaSuccess(token: string): void {
  setFieldValue('captcha_token', token);
}

function handleCaptchaError(): void {
  setFieldValue('captcha_token', '');
}

function handlResetCapcha(): void {
  if (captchaRef.value) captchaRef.value.reset();
  setFieldValue('captcha_token', '');
}

watch(step, handlResetCapcha);

function handleClick(e: Event): void {
  const target = e.target as HTMLElement;

  switch (target.id) {
    case 'resendOTP':
      if (+new Date(values.next_otp_at) > now.value || mutationResendOTP.isPending.value) return;
      tracker({
        id: 'sign_up',
        action: 'click',
        data: { target: 'resend_otp' },
      });
      resendOTP();
      break;
    case 'LOGIN':
      tracker({
        id: 'sign_up',
        action: 'click',
        data: { target: 'login' },
      });
      emits('close');
      openDialog('login');
      break;
    case 'TC':
      tracker({
        id: 'sign_up',
        action: 'click',
        data: { target: 'tac' },
      });
      openDialog('tac', {
        onClose: () => setFieldValue('tac', true),
      });
      break;

    default:
      break;
  }
}

onMounted(async () => {
  await nextTick();
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #btnTopLeft v-if="step !== FORM_STEPS.MOBILE_NUMBER">
      <Button track-id="disable-track" shape="square" variant="secondary" @click="handleBack">
        <Icon name="arrow-left" :size="14"></Icon>
      </Button>
    </template>
    <template #header>
      <div v-html="t('SIGNUP_FORM_TITLE')"></div>
    </template>

    <q-form @submit.prevent="onSubmit" class="flex flex-col flex-nowrap">
      <section v-show="step === FORM_STEPS.MOBILE_NUMBER">
        <div class="text-center mb-4" v-html="t('SIGNUP_FORM_DESC')"></div>
        <VeeInputCountry
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="country = $event"
          autofocus
          :custom-countries="CUSTOM_COUNTRIES"
        />
      </section>

      <section v-show="step === FORM_STEPS.OTP">
        <div class="text-sm text-center mb-5">
          <span v-html="t('SIGNUP_FORM_OTPTEXT_1')"></span><br />
          <b class="text-xl">
            {{ beautifyPhoneNumber(formatMobileNumber(values.mobile_number)) }}
          </b>
          <br />
          <span v-html="t('SIGNUP_FORM_OTPTEXT_3')"></span>
        </div>
        <VeeOTP class="mb-5" name="otp" :num-inputs="OTP_CONFIG.LENGTH" autofocus />

        <div class="text-xs text-center mb-3" v-if="+new Date(values.next_otp_at) > now">
          {{
            t('OTP_VALID_UNTIL', {
              TIME: dateTimeFormat(
                (values.expire_at && new Date(values.expire_at)) ||
                  dayjs().add(OTP_CONFIG.EXPIRY_MINUTES, 'minute').toDate().toISOString(),
                FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
              ),
              TIMEZONE: dayjs().format('Z').replace(':00', ''),
            })
          }}
        </div>
        <div
          class="text-xs text-center mb-5"
          v-html="
            +new Date(values.next_otp_at) > now
              ? t('SIGNUP_FORM_OTPTEXT_RESEND', {
                  TIME: timeCountDown(+new Date(values.next_otp_at) - now),
                })
              : t('SIGNUP_FORM_OTPTEXT_RESEND_NOCOUNTDOWN')
          "
        ></div>
      </section>

      <section v-show="step === FORM_STEPS.PASSWORD">
        <div class="text-sm text-center mb-5" v-html="t('SIGNUP_FORM_PASSWORDDESC')"></div>
        <VeeInput class="mb-5" name="password" :label="t('PASSWORD')" type="password" />
        <Requirements
          class="mb-3 -mt-4"
          v-if="values.password && !matched"
          :password="values.password"
          @valid="matched = $event"
        />

        <VeeInput class="mb-5" name="cf_password" :label="t('RE_ENTER_PASSWORD')" type="password" />

        <VeeCheckBox
          class="mb-1"
          name="tac"
          :label="t('SIGNUP_TNC_CHECKBOX')"
          @click="handleClick"
        />
      </section>

      <TurnstileCaptcha
        v-if="!isTestingEnv"
        ref="captchaRef"
        class="mb-5"
        @success="handleCaptchaSuccess"
        @error="handleCaptchaError"
      />

      <div class="text-center mt-2">
        <Button
          track-id="disable-track"
          :disable="!values.captcha_token && !isTestingEnv"
          :label="
            step === FORM_STEPS.PASSWORD
              ? t('SIGNUP_FORM_CREATEACCBUTTON')
              : t('SIGNUP_FORM_BUTTON')
          "
          :loading="mutationSignUp.isPending.value"
          type="submit"
        />
      </div>
      <div class="text-xs text-center mt-5" v-if="step === 1" v-html="t('SIGNUP_FORM_LOGIN')"></div>
    </q-form>
  </Dialog>
</template>
