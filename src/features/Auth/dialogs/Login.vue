<script lang="ts" setup>
import { LocalStorage } from 'quasar';
import { useForm } from 'vee-validate';
import { useSVStore, useUserStore } from '@stores';
import { TurnstileCaptcha } from '../components';
import { useFetchQueries, usePageTracker } from '@composables';
import { APIResponseError, CountryRegion, LoginPayload, TUserLang, User } from '@types';
import { useChangeLanguageMutation, useUserLoginMutation } from '@services';
import { STORAGE_KEYS } from '@enums';
import * as yup from 'yup';

interface Emits {
  (event: 'close'): void;
}

interface Props {
  resetPW?: boolean;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const ERROR_TYPES = {
  BANNED_NUMBER: 'banned_number',
  LOCKED: 'locked',
  INVALID_CREDENTIALS: 'invalid_credentials',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

const storeUser = useUserStore();
const storeSV = useSVStore();
const loginMutation = useUserLoginMutation();
const changeLangMutation = useChangeLanguageMutation();

const { openDialog } = useMicroRoute();
const { t, locale } = useI18n();
const { trackWithLocation, tracker } = usePageTracker();
const { turnstileDumyToken, isTestingEnv } = storeToRefs(storeUser);
const { userQuery, svUserQuery, svUserBalanceQuery } = useFetchQueries();

const captchaRef = ref<InstanceType<typeof TurnstileCaptcha> | null>(null);
const error = ref<string>('');
const country = ref<CountryRegion>();

const loading = computed(() => loginMutation.isPending.value);

const validationSchema = yup.object({
  mobile_number: yup.string().required(t('MOBILE_NUMBER_REQUIRED')),
  password: yup.string().required(t('PASSWORD_REQUIRED')),
});

const { handleSubmit, values, setFieldValue } = useForm<LoginPayload>({
  initialValues: {
    mobile_number: '',
    password: '',
    captcha_token: '',
  },
  validationSchema,
});

watch(() => [values.mobile_number, values.password], clearError);

function clearError(): void {
  error.value = '';
}

function formatMobileNumber(mobileNumber: string): string {
  return `${country.value?.code}${mobileNumber}`.replace(/\+/g, '');
}

async function handleLanguageUpdate(userLang: TUserLang): Promise<void> {
  try {
    LocalStorage.set(STORAGE_KEYS.LANGUAGE, userLang);
    await changeLangMutation.mutateAsync(userLang);
    storeUser.updateUser({ lang: userLang });
    locale.value = userLang;
  } catch (error) {
    console.error('Error updating language:', error);
  }
}

async function handleLoginSuccess(data: { user: User; token: string }): Promise<void> {
  storeUser.setToken(data.token);
  storeSV.setToken(data.user.sv_token);
  if (data.user.sv_token) {
    await Promise.all([svUserQuery.refetch(), svUserBalanceQuery.refetch()]);
  }
  await userQuery.refetch();
}

function handleLoginError(err: APIResponseError): void {
  const { data, error_message } = err;

  const errorHandlers = {
    [ERROR_TYPES.BANNED_NUMBER]: () => {
      emits('close');
      openDialog('locked_account', {
        lock_until: data?.info?.lock_until,
        type: 'banned',
      });
    },
    [ERROR_TYPES.LOCKED]: () => {
      emits('close');
      openDialog('locked_account', {
        lock_until: data?.info?.lock_until,
        type: 'locked',
      });
    },
    [ERROR_TYPES.INVALID_CREDENTIALS]: () => {
      error.value = t('LOGIN_ERROR', {
        ATTEMPTS: data?.info?.remaining_attempts,
      });
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(error_message);
    },
  };

  const handler = errorHandlers[error_message as ErrorType] || errorHandlers[ERROR_TYPES.DEFAULT];
  handler();
  resetCaptcha();
}

const handleLogin = handleSubmit(async (formValues): Promise<void> => {
  try {
    const mobile_number = formatMobileNumber(formValues.mobile_number);
    const payload = {
      mobile_number,
      password: formValues.password,
      captcha_token: isTestingEnv.value ? turnstileDumyToken.value : formValues.captcha_token,
    };
    const data = await loginMutation.mutateAsync(payload);
    trackWithLocation({
      id: 'login',
      action: 'click',
      data: {
        target: 'login_success',
        mobile_number,
      },
    });
    await handleLoginSuccess(data);
    if (data.user?.lang) await handleLanguageUpdate(data.user.lang);
    emits('close');
  } catch (err) {
    handleLoginError(err as APIResponseError);
  }
});

function handleForgotPW(): void {
  emits('close');
  openDialog('forgot_password');
}

function handleCaptchaSuccess(token: string): void {
  setFieldValue('captcha_token', token);
}

function handleCaptchaError(): void {
  setFieldValue('captcha_token', '');
}

function resetCaptcha(): void {
  if (captchaRef.value) captchaRef.value.reset();
  setFieldValue('captcha_token', '');
}

function handleClick(e: Event): void {
  const target = e.target as HTMLElement;
  if (target.id === 'SIGNUP') {
    tracker({
      id: 'login',
      action: 'click',
      data: {
        target: 'signup',
      },
    });
    emits('close');
    openDialog('sign_up');
  }
}

onMounted(async () => {
  await nextTick();
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('LOGIN_TITLE')" />
    </template>

    <q-form @submit.prevent="handleLogin" class="flex flex-col flex-nowrap">
      <div
        v-if="resetPW"
        class="text-center text-sm mb-5 bg-[#03833E] rounded p-2"
        v-html="t('LOGIN_RESETPW_DESC')"
      />
      <div v-else class="text-center text-sm mb-5" v-html="t('LOGIN_DESCRIPTION')" />

      <VeeInputCountry
        class="mb-5"
        :label="t('MOBILE_NUMBER')"
        name="mobile_number"
        :error="!!error"
        autofocus
        @update:country="country = $event"
      />

      <VeeInput
        name="password"
        :label="t('PASSWORD')"
        type="password"
        class="mb-5"
        :error="!!error"
      />

      <div
        class="text-xs text-right mt-2 underline cursor-pointer"
        v-html="t('LOGIN_FORGOTPASSWORD')"
        v-tracker="{
          id: 'login',
          action: 'click',
          data: {
            target: 'forgot_password',
          },
        }"
        @click="handleForgotPW"
      />

      <div v-if="error" class="card-error mt-2 text-center" v-html="error" />

      <TurnstileCaptcha
        v-if="!isTestingEnv"
        ref="captchaRef"
        class="mb-5"
        @success="handleCaptchaSuccess"
        @error="handleCaptchaError"
      />

      <div class="text-center mt-5">
        <Button
          track-id="disable-track"
          :disable="!values.captcha_token && !isTestingEnv"
          :label="t('LOGIN_BUTTON')"
          :loading="loading"
          type="submit"
        />
      </div>

      <div class="text-xs text-center mt-5" v-html="t('LOGIN_SIGNUP')" />
    </q-form>
  </Dialog>
</template>
