<script setup lang="ts">
import { beautifyPhoneNumber, metaPixelTacking } from '@utils';
import { User } from '@types';
import { useFetchQueries, usePageTracker } from '@composables';
import gsap, { Linear } from 'gsap';

const SELECTORS = {
  FLARE: '.upload_flare',
  TEXT: '.text',
  STAR: '.upload_star',
} as const;

interface Props {
  user: User;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { tracker } = usePageTracker();
const { userQuery } = useFetchQueries();

let animationTimeline: gsap.core.Timeline | null = null;

function createFlareAnimation(): gsap.core.Timeline {
  const tl = gsap.timeline();

  return tl
    .fromTo(
      SELECTORS.FLARE,
      { scale: 0 },
      {
        scale: 1,
        duration: 1,
        delay: 0.5,
      },
    )
    .fromTo(
      SELECTORS.FLARE,
      { rotate: 0 },
      {
        rotate: 720,
        duration: 20,
        repeat: -1,
        ease: Linear.easeNone,
      },
      '-=1',
    );
}

function createTextAnimation(): gsap.core.Tween {
  return gsap.fromTo(
    SELECTORS.TEXT,
    { scale: 0 },
    {
      scale: 1,
      duration: 0.5,
      stagger: 0.2,
    },
  );
}

function createStarAnimation(): gsap.core.Tween {
  return gsap.fromTo(
    SELECTORS.STAR,
    { opacity: 0 },
    {
      opacity: 1,
      yoyo: true,
      repeat: -1,
      delay: 1,
      duration: 1,
    },
  );
}

function startAnimations(): void {
  animationTimeline = createFlareAnimation();
  createTextAnimation();
  createStarAnimation();
}

function cleanupAnimations(): void {
  if (animationTimeline) {
    animationTimeline.kill();
    animationTimeline = null;
  }
  gsap.killTweensOf(SELECTORS.STAR);
}

const formattedMobileNumber = computed(() => beautifyPhoneNumber(props.user.mobile_number));

const hunterId = computed(() => props.user.hunter_id);

function handleBackToMap(): void {
  emits('close');
  tracker({
    id: 'sign_up_success',
    action: 'click',
    data: {
      target: 'back_to_map',
    },
  });
}

function handleQuestionMark(): void {
  openDialog('dont_like_hunter_id');
}

onMounted(async () => {
  await nextTick();
  void userQuery.refetch();
  metaPixelTacking('CreateAccount');
  startAnimations();
});

onBeforeUnmount(() => {
  cleanupAnimations();
});
</script>

<template>
  <div
    class="fullscreen signup-success column flex-nowrap items-center text-center justify-between py-[40px]"
  >
    <div class="font-bold text-lg" v-html="t('SIGNUP_CREATED_TITLE')" />

    <div class="absolute absolute-center full-width flex flex-center h-[100vw]">
      <Icon class="upload_flare absolute pointer-none top-0 left-0 w-[100vw]" name="upload_flare" />
      <Icon
        class="upload_star full-width absolute pointer-none top-0 left-0 z-10"
        name="star_frame"
      />
    </div>

    <div class="px-5 text">
      <div class="text-sm" v-html="t('SIGNUP_CREATED_DESC')" />

      <div class="text-2xl font-bold mt-2" v-html="t('SIGNUP_CREATED_HUNTER')" />

      <div class="row flex-nowrap items-center mt-1">
        <div class="font-bold whitespace-nowrap hunter-id-display">
          {{ hunterId }}
        </div>

        <div class="ml-3" @click="handleQuestionMark">
          <Icon name="question-mark" :size="20" />
        </div>
      </div>

      <div class="text-lg mt-1">
        {{ formattedMobileNumber }}
      </div>
    </div>

    <div class="flex justify-center flex-col items-center gap-5">
      <Button track-id="disable-track" :label="t('BUTTON_BACKTOMAP')" @click="handleBackToMap" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.signup-success {
  overflow-x: hidden;
  background-color: #090422;
}

.hunter-id-display {
  font-size: 15vw;
  line-height: 18vw;
}
</style>
