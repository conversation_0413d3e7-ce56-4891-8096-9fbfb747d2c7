import { Home } from './views';

class GameManagerPlugin extends GamePlugin {
  override getPublicAPI<T>(): T {
    throw new Error('Method not implemented.');
  }
}

export const MapPlugin = new GameManagerPlugin(
  'MapPlugin',
  [
    {
      path: 'home',
      component: Home,
      bgm: 'default',
    },
  ],
  [],
  [
    // {
    //   name: 'menu-1',
    //   component: defineAsyncComponent(() => import('./controls/Menu.vue')),
    //   activated: true,
    // },
    // {
    //   name: 'menu-2',
    //   component: defineAsyncComponent(() => import('./controls/Menu2.vue')),
    //   activated: false,
    // },
  ],
);
