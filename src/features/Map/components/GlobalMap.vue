<script lang="ts" setup>
import { useMapStore } from '@stores';
import { useGlobalMap } from '../composables';
import { GeolocateControls, Mapbox } from 'vue3-maplibre-gl';
import 'vue3-maplibre-gl/dist/style.css';

const storeMap = useMapStore();

const { isMapLoaded } = storeToRefs(storeMap);
const {
  MAP_OPTIONS,
  GEOLOCATE_OPTIONS,
  registerMap,
  registerGeoInstance,
  setGeoInstance,
  handleErrorGPS,
  handleSuccessGPS,
} = useGlobalMap();
</script>
<template>
  <Mapbox
    debug
    :options="MAP_OPTIONS"
    @click="
      (e) => {
        console.log(e);
      }
    "
    @register="registerMap"
  >
    <template v-if="isMapLoaded">
      <GeolocateControls
        :options="GEOLOCATE_OPTIONS"
        @geolocate="handleSuccessGPS"
        @error="handleErrorGPS"
        @trackuserlocationend="setGeoInstance"
        @trackuserlocationstart="setGeoInstance"
        @register="registerGeoInstance"
      />
    </template>
  </Mapbox>
</template>
