<template>
  <div class="fixed w-0 h-0 overflow-visible">
    <Button
      class="fixed bottom-4 right-4 z-10"
      variant="primary"
      @click="
        toggleControl('menu-1', false);
        toggleControl('menu-2', true);
      "
    >
      <template #icon>
        <Icon name="menu" />
      </template>
      Menu 2
    </Button>
    <Button
      class="fixed top-4 left-4 z-10"
      variant="primary"
      @click="
        toggleControl('menu-1', false);
        toggleControl('menu-2', true);
      "
    >
      <template #icon>
        <Icon name="menu" />
      </template>
      Menu 3
    </Button>
    <Button
      class="fixed top-4 right-4 z-10"
      variant="primary"
      @click="
        toggleControl('menu-1', false);
        toggleControl('menu-2', true);
      "
    >
      <template #icon>
        <Icon name="menu" />
      </template>
      Menu 4
    </Button>
  </div>
</template>

<script lang="ts" setup>
const { toggleControl } = useMicroRoute();
</script>
