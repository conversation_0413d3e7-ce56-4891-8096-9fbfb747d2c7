<script setup lang="ts">
import { useGlobalTriggerDialog, useSystemMessage } from '@composables';
import { GlobalMap } from '../components';
import { useMapStore, useUserStore } from '@stores';

const storeUser = useUserStore();
const storeMap = useMapStore();

const { user } = storeToRefs(storeUser);
const { isMapLoaded } = storeToRefs(storeMap);
const { push, openDialog } = useMicroRoute();

useGlobalTriggerDialog();
useSystemMessage();

const isBanned = computed(() => !!user.value?.banned);

watch(isMapLoaded, (loaded) => {
  if (loaded && isBanned.value) {
    openDialog('locked_account', { type: 'banned' });
  }
});
</script>

<template>
  <div>
    <Button
      track-id="disable-track"
      label="Open Dialog"
      @click="openDialog('login')"
      class="mb-10"
    />
    <Button track-id="disable-track" label="Open Page" @click="push('menu')" class="mb-10" />
  </div>
  <GlobalMap />
  <UnifyInstructor />
</template>
