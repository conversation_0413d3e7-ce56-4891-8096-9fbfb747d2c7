import { FillLayerStyle, LineLayerStyle, SymbolLayerStyle } from 'vue3-maplibre-gl';

export const BOUNDARY_LINE_LAYER: LineLayerStyle = {
  'line-color': '#F59200',
  'line-width': 14,
  'line-opacity': 0.7,
  'line-join': 'bevel',
  'line-round-limit': 1,
};

export const BOUNDARY_FUTURE_LINE_LAYER: FillLayerStyle = {
  'fill-color': '#11D1F9',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.3, 14, 0.075, 19, 0],
};

export const BOUNDARY_FUTURE_OUTLINE_LAYER: LineLayerStyle = {
  'line-color': '#11D1F9',
  'line-dasharray': [2, 1],
};

export const BOUNDARY_FUTURE_OUTLINE_GLOW_LAYER: LineLayerStyle = {
  'line-width': ['interpolate', ['linear'], ['zoom'], 9, 1, 11, 10, 14, 20],
  'line-blur': 10,
  'line-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.5, 11, 0.3],
  'line-color': '#11D1F9',
};

export const BASE_PAINT_CIRCLE: LineLayerStyle = {
  'line-width': ['interpolate', ['linear'], ['zoom'], 9, 1, 11, 10, 14, 20],
  'line-blur': 10,
  'line-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.5, 11, 0.3],
};

export const ONGOING_COIN_FREE_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#ffffff',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.3, 14, 0.075, 19, 0],
};

export const ONGOING_COIN_FREE_LINE_LAYER: LineLayerStyle = {
  'line-color': '#ffffff',
};

export const ONGOING_COIN_FREE_LINE_GLOW_LAYER: LineLayerStyle = {
  ...BASE_PAINT_CIRCLE,
  'line-color': '#ffffff',
};

export const ONGOING_COIN_PAID_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#ff0000',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.4, 14, 0.1, 19, 0],
};

export const METAL_SONAR_NEGATIVE_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#DF3126',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.3, 14, 0.4, 19, 0.3],
};

export const METAL_SONAR_POSITIVE_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#29D798',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.3, 14, 0.4, 19, 0.3],
};

export const METAL_SONAR_NEGATIVE_LINE_LAYER: LineLayerStyle = {
  'line-color': '#DF3126',
};

export const METAL_SONAR_POSITIVE_LINE_LAYER: LineLayerStyle = {
  'line-color': '#29D798',
};

export const ONGOING_COIN_PAID_LINE_LAYER: LineLayerStyle = {
  'line-color': '#FF0000',
};

export const ONGOING_COIN_PAID_LINE_GLOW_LAYER: LineLayerStyle = {
  ...BASE_PAINT_CIRCLE,
  'line-color': '#FF0000',
};

export const VERIFYING_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#FF00FF',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.1, 14, 0.025, 19, 0],
};

export const VERIFYING_LINE_LAYER: LineLayerStyle = {
  'line-color': '#FF84E4',
};

export const VERIFYING_LINE_GLOW_LAYER: LineLayerStyle = {
  ...BASE_PAINT_CIRCLE,
  'line-color': '#FF84E4',
};

export const FORFEITED_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#A01C1C',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.4, 14, 0.1, 19, 0],
};

export const FORFEITED_LINE_LAYER: LineLayerStyle = {
  'line-color': '#A01C1C',
};

export const FORFEITED_LINE_GLOW_LAYER: LineLayerStyle = {
  ...BASE_PAINT_CIRCLE,
  'line-color': '#A01C1C',
};

export const TIME_UP_FILL_LAYER: FillLayerStyle = {
  'fill-color': 'rgba(0, 0, 0, 0.3)',
};

export const TIME_UP_LINE_LAYER: LineLayerStyle = {
  'line-color': '#8D8D8D',
  'line-width': 2,
};

export const TIME_UP_BLOCK_LAYER: LineLayerStyle = {
  'line-width': ['interpolate', ['linear'], ['zoom'], 9, 1, 14, 10],
  'line-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.5, 14, 1],
  'line-color': '#505050',
};

export const SPONSOR_LAYOUT: SymbolLayerStyle = {
  'icon-anchor': 'bottom',
  'icon-image': ['get', 'brand_id'],
  'icon-size': [
    'interpolate',
    ['linear'],
    ['zoom'],
    13,
    ['get', 'iconSizeMin'],
    18,
    ['get', 'iconSizeMax'],
  ],
  'icon-offset': [
    'interpolate',
    ['linear'],
    ['zoom'],
    13,
    [
      'match',

      ['get', 'hardcode_key'],
      ['suntec_1'],
      ['literal', [0, -80]],

      ['milksha_1'],
      ['literal', [0, 80]],

      ['milksha_2'],
      ['literal', [-50, 0]],

      ['literal', [0, 0]],
    ],
    19,
    [
      'match',
      ['get', 'hardcode_key'],
      ['suntec_1'],
      ['literal', [0, 0]],

      ['milksha_1'],
      ['literal', [0, 0]],

      ['milksha_2'],
      ['literal', [0, 0]],

      ['literal', [0, 0]],
    ],
  ],
  'icon-allow-overlap': true,
  'symbol-sort-key': ['case', ['==', ['get', 'tier'], 2], 1, ['==', ['get', 'tier'], 1], 2, 0],
};

export const FOUND_COIN_SYMBOL_LAYER: SymbolLayerStyle = {
  'icon-anchor': 'bottom',
  'icon-image': ['get', 'icon'],
  'icon-size': [
    'interpolate',
    ['linear'],
    ['zoom'],
    9,
    ['get', 'iconSizeMin'],
    20,
    ['get', 'iconSizeMax'],
  ],
  'icon-allow-overlap': true,
  'icon-rotation-alignment': 'viewport',
};

export const SPONSOR_LAYER: SymbolLayerStyle = {
  ...SPONSOR_LAYOUT,
  'text-halo-color': '#000000',
  'icon-opacity': ['interpolate', ['linear'], ['zoom'], 11, 1],
};

export const GRID_GOLDEN_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#FFB23C',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.9, 12, 0.8, 13, 0.4, 19, 0],
};

export const GRID_GOLDEN_LINE_LAYER: LineLayerStyle = {
  'line-color': '#1A7B91',
  'line-width': 1,
  'line-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.1, 12, 1],
};

export const GRID_ELIMINATED_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#311A63',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.9, 12, 0.8, 13, 0.4, 19, 0],
};

export const GRID_ELIMINATED_LINE_LAYER: LineLayerStyle = {
  'line-color': '#1A7B91',
  'line-width': 1,
  'line-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.4, 12, 1],
};

export const GRID_PAID_ELIMINATED_FILL_LAYER: FillLayerStyle = {
  'fill-color': '#780c0c',
  'fill-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.9, 12, 0.8, 13, 0.4, 19, 0],
};

export const GRID_PAID_ELIMINATED_LINE_LAYER: LineLayerStyle = {
  'line-color': '#800000',
  'line-width': ['interpolate', ['linear'], ['zoom'], 9, 1, 13, 3],
  'line-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.5, 13, 1],
};

export const LAYERS: Record<string, any> = {
  sv5: {
    maxzoom: 24,
    layout: {
      'icon-size': {
        stops: [
          [10, 0.05],
          [12, 0.1],
          [15, 0.35],
        ],
      },
      'icon-allow-overlap': true,
      'text-offset': {
        stops: [
          [10.7, [100, 0]],
          [20, [0, 0]],
        ],
      },
      visibility: 'visible',
    },
    paint: {
      'icon-opacity': {
        stops: [
          [9.7, 0],
          [10, 1],
        ],
      },
    },
  },
  sv4: {
    maxzoom: 24,
    layout: {
      'icon-size': {
        stops: [
          [10, 0.05],
          [12, 0.1],
          [15, 0.35],
        ],
      },
      'icon-allow-overlap': true,
      'text-offset': {
        stops: [
          [10.7, [0, -200]],
          [20, [0, 0]],
        ],
      },
      visibility: 'visible',
    },
    paint: {
      'icon-opacity': {
        stops: [
          [9.7, 0],
          [10, 1],
        ],
      },
    },
  },
  sv3: {
    maxzoom: 24,
    layout: {
      'icon-size': {
        stops: [
          [10, 0.05],
          [12, 0.1],
          [15, 0.35],
        ],
      },
      'icon-allow-overlap': true,
      'text-offset': {
        stops: [
          [10.7, [120, 0]],
          [20, [0, 0]],
        ],
      },
      visibility: 'visible',
    },
    paint: {
      'icon-opacity': {
        stops: [
          [9.7, 0],
          [10, 1],
        ],
      },
    },
  },
  sv1: {
    maxzoom: 24,
    layout: {
      'icon-size': {
        stops: [
          [10, 0.05],
          [12, 0.1],
          [15, 0.35],
        ],
      },
      'icon-allow-overlap': true,
      'text-offset': {
        stops: [
          [10.7, [0, 0]],
          [18, [0, 0]],
        ],
      },
      visibility: 'visible',
    },
    paint: {
      'icon-opacity': {
        stops: [
          [9.7, 0],
          [10, 1],
        ],
      },
    },
  },
  sv2: {
    maxzoom: 24,
    layout: {
      'icon-size': {
        stops: [
          [10, 0.05],
          [12, 0.1],
          [15, 0.35],
        ],
      },
      'icon-allow-overlap': true,
      'text-offset': {
        stops: [
          [10.7, [-150, 0]],
          [20, [0, 0]],
        ],
      },
      visibility: 'visible',
    },
    paint: {
      'icon-opacity': {
        stops: [
          [9.7, 0],
          [10, 1],
        ],
      },
    },
  },
  Sentosa: {
    maxzoom: 24,
    layout: {
      'icon-size': {
        stops: [
          [10, 0.0001],
          [12, 0.05],
          [17, 0.35],
        ],
      },
      'icon-allow-overlap': true,
      'text-offset': {
        stops: [
          [10.7, [0, 0]],
          [18, [0, 0]],
        ],
      },
      visibility: 'visible',
    },
    paint: {
      'icon-opacity': {
        stops: [
          [9.7, 0],
          [10, 1],
        ],
      },
    },
  },
  'Sentosa 2': {
    maxzoom: 24,
    layout: {
      'icon-size': {
        stops: [
          [7, 0.0001],
          [10.7, 0.001],
          [17, 0.35],
        ],
      },
      'icon-allow-overlap': true,
      'text-offset': [0, 100],
      visibility: 'visible',
      'icon-offset': {
        stops: [
          [10.7, [0, -150]],
          [20, [0, 0]],
        ],
      },
    },
    paint: {
      'icon-opacity': {
        stops: [
          [9.7, 0],
          [10, 1],
        ],
      },
    },
  },
  'Sentosa 3': {
    maxzoom: 24,
    layout: {
      'icon-size': {
        stops: [
          [10, 0.0001],
          [12, 0.05],
          [17, 0.35],
        ],
      },
      'icon-allow-overlap': true,
      'text-offset': [0, 0],
      visibility: 'visible',
      'icon-offset': {
        stops: [
          [10.7, [0, 250]],
          [20, [0, 0]],
        ],
      },
    },
    paint: {
      'icon-opacity': {
        stops: [
          [9.7, 0],
          [10, 1],
        ],
      },
    },
  },
};
