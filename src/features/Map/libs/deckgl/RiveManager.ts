import { Device, Texture } from '@luma.gl/core';
import { Rive } from '@rive-app/canvas';

export type RiveConfig = {
  width: number;
  height: number;
  anchorX?: number;
  anchorY?: number;
  atlast: string;
  animationName?: string;
  artboard: string;
  stateMachine: string;
  device: Device;
  iconMapping: Record<string, number[]>;
};

export default class RiveManager {
  private _texture: Texture | null = null;
  private _config: RiveConfig;
  private _canvas: HTMLCanvasElement;
  private _ctx: CanvasRenderingContext2D;
  private device: Device;
  private _iconsMapping: Record<string, number[]>;
  constructor(config: RiveConfig) {
    this._config = config;
    this.device = config.device;
    this._canvas = document.createElement('canvas');
    this._canvas.width = config.width;
    this._canvas.height = config.height;
    this._canvas.style.position = 'absolute';
    this._canvas.style.pointerEvents = 'none';
    this._canvas.style.visibility = 'hidden';
    this._iconsMapping = config.iconMapping;

    document.body.appendChild(this._canvas);

    const ctx = this._canvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) throw new Error('Failed to get 2D context for RiveManager');
    this._ctx = ctx;
    this._initRive();
  }
  private _initRive() {
    new Rive({
      src: this._config.atlast,
      canvas: this._canvas,
      autoplay: true,
      artboard: this._config.artboard,
      stateMachines: this._config.stateMachine,
    });
  }
  public getIconMapping(icon: string) {
    return this._iconsMapping[icon];
  }
  getTexture(): Texture | null {
    if (!this._texture)
      this._texture = this.device.createTexture({
        format: 'rgba8unorm', // Correct format for luma.gl
        width: this._config.width,
        height: this._config.height,
        data: this._ctx.getImageData(0, 0, this._config.width, this._config.height),
      });
    this._texture.copyExternalImage({
      image: this._ctx.getImageData(0, 0, this._config.width, this._config.height),
    });
    return this._texture;
  }
}
