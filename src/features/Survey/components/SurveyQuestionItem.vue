<script lang="ts" setup>
import { SurveyAnswer, SurveyQuestion } from '@types';

interface Props {
  questions: SurveyQuestion[];
  loading: boolean;
  initialQuestionIndex?: number;
}

interface Emits {
  (event: 'next', questionIndex: number, questions: SurveyQuestion[]): void;
  (event: 'completed', questions: SurveyQuestion[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  questions: () => [],
  initialQuestionIndex: 0,
});

const emits = defineEmits<Emits>();

const { t } = useI18n();

const questionNumber = ref(props.initialQuestionIndex);
const questions = ref<SurveyQuestion[]>([...props.questions]);

const currentQuestion = computed(() => questions.value[questionNumber.value]);
const hasQuestions = computed(() => questions.value.length > 0);
const isLastQuestion = computed(() => questionNumber.value === questions.value.length - 1);
const progressText = computed(() =>
  t('SURVEY_POPUP_QUESTION_NUMBER', {
    CURRENT: questionNumber.value + 1,
    TOTAL: questions.value.length,
  }),
);

const isNextButtonDisabled = computed(() => {
  if (!currentQuestion.value) return true;

  const { type, multiple, a: answers } = currentQuestion.value;

  if (multiple) {
    return (
      answers.every((answer) => !answer.selected) ||
      answers.some((answer) => !answer.value && answer.selected && answer.type === 'area')
    );
  }

  if (type === 'select') {
    return answers.every((answer) => !answer.selected);
  }

  return answers.every((answer) => !answer.value);
});

function toggleAnswerSelection(targetAnswer: SurveyAnswer): void {
  if (!currentQuestion.value) return;

  const currentAnswers = currentQuestion.value.a;
  const updatedAnswers = currentAnswers.map((answer) => ({
    ...answer,
    selected: answer === targetAnswer ? !(answer.selected || false) : false,
  }));

  // Update the specific question's answers
  questions.value[questionNumber.value] = {
    ...currentQuestion.value,
    a: updatedAnswers,
  };
}

function updateAnswerValue(answerIndex: number, value: string | number | null): void {
  if (!currentQuestion.value || value == null) return;

  const updatedAnswers = [...currentQuestion.value.a];
  updatedAnswers[answerIndex] = {
    ...updatedAnswers[answerIndex],
    value,
  };

  questions.value[questionNumber.value] = {
    ...currentQuestion.value,
    a: updatedAnswers,
  };
}

function handleMultipleSelectionToggle(answer: SurveyAnswer): void {
  if (!currentQuestion.value) return;

  const updatedAnswers = currentQuestion.value.a.map((item) => ({
    ...item,
    selected: item === answer ? !(item.selected || false) : item.selected || false,
  }));

  questions.value[questionNumber.value] = {
    ...currentQuestion.value,
    a: updatedAnswers,
  };
}

function selectTextareaAnswer(answer: SurveyAnswer): void {
  if (answer.type === 'area') {
    answer.selected = true;
  }
}

function handleNextQuestion(): void {
  if (!currentQuestion.value || isNextButtonDisabled.value) return;

  emits('next', questionNumber.value, questions.value);

  if (isLastQuestion.value) emits('completed', questions.value);
  else questionNumber.value++;
}

// Watchers
watch(
  () => props.questions,
  (newQuestions) => {
    if (newQuestions && newQuestions.length > 0) {
      questions.value = [...newQuestions];
    }
  },
  { deep: true, immediate: true },
);

watch(
  () => props.initialQuestionIndex,
  (newIndex) => {
    if (newIndex >= 0 && newIndex < questions.value.length) {
      questionNumber.value = newIndex;
    }
  },
  { immediate: true },
);

onMounted(() => {
  if (props.questions.length > 0) {
    questions.value = [...props.questions];
  }
});
</script>
<template>
  <Transition name="question" mode="out-in">
    <div v-if="hasQuestions" :key="questionNumber">
      <div class="mb-1 text-sm opacity-50" v-html="progressText"></div>

      <div class="mb-2 text-lg" v-html="currentQuestion?.q"></div>

      <div v-if="currentQuestion?.sub_q" class="mb-5 text-sm" v-html="currentQuestion.sub_q"></div>

      <!-- Multiple choice questions -->
      <template v-if="currentQuestion?.multiple">
        <div class="mb-10">
          <div class="flex flex-col gap-2">
            <div
              v-for="(answer, index) in currentQuestion.a"
              :key="index"
              class="bg-[#12caf366] text-sm rounded-lg opacity-50 p-2 transition-opacity duration-200 cursor-pointer"
              :class="{ '!opacity-100': answer.selected }"
              @click="handleMultipleSelectionToggle(answer)"
            >
              <!-- Textarea type -->
              <div v-if="answer.type === 'area'" class="flex flex-col gap-2">
                <div
                  class="text-sm cursor-pointer"
                  v-html="answer.title"
                  @click.stop="handleMultipleSelectionToggle(answer)"
                ></div>
                <div @click.stop="selectTextareaAnswer(answer)">
                  <q-input
                    :model-value="answer.value"
                    type="textarea"
                    standout
                    dense
                    bg-color="dark"
                    color="white"
                    :placeholder="t('SURVEY_POPUP_AREA_PLACEHOLDER')"
                    @update:model-value="(value) => updateAnswerValue(index, value)"
                    @focus="selectTextareaAnswer(answer)"
                  />
                </div>
              </div>

              <!-- Image type -->
              <div v-else-if="answer.type === 'image'" class="flex flex-col gap-2">
                <div class="text-sm" v-html="answer.title"></div>
                <Icon
                  :name="String(answer.image)"
                  class="object-cover w-full h-max"
                  :alt="answer.title || 'Survey option image'"
                />
              </div>

              <!-- Default text type -->
              <div v-else v-html="answer.value"></div>
            </div>
          </div>
        </div>
      </template>

      <!-- Single choice questions -->
      <template v-else-if="currentQuestion">
        <div class="mb-10">
          <!-- Rating type -->
          <section v-if="currentQuestion.type === 'rate'">
            <div v-for="(answer, index) in currentQuestion.a" :key="index">
              <div class="mb-2 text-center">
                <q-slider
                  class="mt-8"
                  :markers="true"
                  :label-always="true"
                  :model-value="Number(answer.value)"
                  :min="0"
                  :max="answer.total || 10"
                  @update:model-value="(value) => updateAnswerValue(index, Number(value))"
                />
              </div>
              <div class="flex justify-between text-xs opacity-70">
                <div v-html="currentQuestion.min_rate_text"></div>
                <div v-html="currentQuestion.max_rate_text"></div>
              </div>
            </div>
          </section>

          <!-- Textarea type -->
          <section v-else-if="currentQuestion.type === 'area'">
            <q-input
              v-for="(answer, index) in currentQuestion.a"
              :key="index"
              :model-value="answer.value"
              type="textarea"
              standout
              dense
              bg-color="dark"
              color="white"
              :placeholder="t('SURVEY_POPUP_AREA_PLACEHOLDER')"
              @update:model-value="(value) => updateAnswerValue(index, value)"
            />
          </section>

          <!-- Select type -->
          <section v-else-if="currentQuestion.type === 'select'">
            <div class="flex flex-col gap-2">
              <div
                v-for="(answer, index) in currentQuestion.a"
                :key="index"
                class="bg-[#12caf366] text-sm rounded-lg opacity-50 p-2 transition-opacity duration-200 cursor-pointer"
                :class="{ '!opacity-100': answer.selected }"
                @click="toggleAnswerSelection(answer)"
              >
                <!-- Textarea type -->
                <div v-if="answer.type === 'area'" class="flex flex-col gap-2">
                  <div
                    class="text-sm cursor-pointer"
                    v-html="answer.title"
                    @click.stop="toggleAnswerSelection(answer)"
                  ></div>
                  <div @click.stop="selectTextareaAnswer(answer)">
                    <q-input
                      :model-value="answer.value"
                      type="textarea"
                      standout
                      dense
                      bg-color="dark"
                      color="white"
                      :placeholder="t('SURVEY_POPUP_AREA_PLACEHOLDER')"
                      @update:model-value="(value) => updateAnswerValue(index, value)"
                      @focus="selectTextareaAnswer(answer)"
                    />
                  </div>
                </div>

                <!-- Image type -->
                <div v-else-if="answer.type === 'image'" class="flex flex-col gap-2">
                  <div class="text-sm" v-html="answer.title"></div>
                  <Icon
                    :name="String(answer.image)"
                    class="object-cover w-full h-max"
                    :alt="answer.title || 'Survey option image'"
                  />
                </div>

                <!-- Default text type -->
                <div v-else v-html="answer.value"></div>
              </div>
            </div>
          </section>

          <!-- Slider type -->
          <section v-else-if="currentQuestion.type === 'slide' && currentQuestion">
            <q-slider
              v-for="(answer, index) in currentQuestion.a"
              :key="index"
              class="mt-8 hide-pin"
              :markers="true"
              :label-value="answer.value + (currentQuestion.slide?.unit || '')"
              :label-always="true"
              :model-value="Number(answer.value)"
              :min="0"
              :max="100"
              :step="currentQuestion.slide?.step || 1"
              @update:model-value="(value) => updateAnswerValue(index, Number(value))"
            />
          </section>
        </div>
      </template>

      <div class="text-center">
        <Button
          track-id="disable-track"
          :label="t('SURVEY_BUTTON_NEXT')"
          :disable="isNextButtonDisabled"
          :loading="loading"
          @click="handleNextQuestion"
        />
      </div>
    </div>
  </Transition>
</template>
<style lang="scss" scoped>
.question-enter-active,
.question-leave-active {
  transition: all 0.2s ease-in-out;
}

.question-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.question-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.question-enter-to,
.question-leave-from {
  opacity: 1;
  transform: translateX(0);
}
</style>
