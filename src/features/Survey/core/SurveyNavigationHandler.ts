import { SurveyQuestion } from '@types';

export class SurveyNavigationHandler {
  private static navigationHistory: number[] = [];

  static getNextQuestionIndex(currentIndex: number, questions: SurveyQuestion[]): number {
    const currentQuestion = questions[currentIndex];

    // Track navigation history for proper back navigation
    this.navigationHistory.push(currentIndex);

    if (currentQuestion?.condition?.next) {
      const nextQuestionId = currentQuestion.condition.next(currentQuestion, questions);
      const nextQuestionIndex = questions.findIndex((q) => q.id === nextQuestionId);

      return nextQuestionIndex >= 0 ? nextQuestionIndex : currentIndex + 1;
    }

    return currentIndex + 1;
  }

  static getPreviousQuestionIndex(currentIndex: number, questions: SurveyQuestion[]): number {
    // Use navigation history for accurate back navigation
    if (this.navigationHistory.length > 0) {
      const previousIndex = this.navigationHistory.pop();
      return previousIndex !== undefined ? previousIndex : Math.max(0, currentIndex - 1);
    }

    // Fallback: Find the previous question that would lead to current question
    const currentQuestionId = questions[currentIndex]?.id;

    for (let i = currentIndex - 1; i >= 0; i--) {
      const prevQuestion = questions[i];

      if (prevQuestion?.condition?.next) {
        const nextId = prevQuestion.condition.next(prevQuestion, questions);
        if (nextId === currentQuestionId) return i;
      } else if (currentQuestionId !== undefined && i === currentIndex - 1) {
        return i;
      }
    }

    return Math.max(0, currentIndex - 1);
  }

  static resetNavigationHistory(): void {
    this.navigationHistory = [];
  }

  static getNavigationHistory(): number[] {
    return [...this.navigationHistory];
  }
}
