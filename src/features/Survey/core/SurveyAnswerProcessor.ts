import { SurveyQuestion } from '@types';

export class SurveyAnswerProcessor {
  static getAnswerForSingleSelection(question: SurveyQuestion): unknown {
    return question.a
      .filter((answer) => answer.selected || (question.type !== 'select' && answer.value))
      .map((answer) => answer.value)[0];
  }

  static getAnswerForMultipleSelection(question: SurveyQuestion): unknown[] {
    return question.a.filter((answer) => answer.selected).map((answer) => answer.value);
  }

  static processQuestionAnswer(question: SurveyQuestion): unknown {
    return question.multiple
      ? this.getAnswerForMultipleSelection(question)
      : this.getAnswerForSingleSelection(question);
  }

  static getValidNavigationPath(questions: SurveyQuestion[]): SurveyQuestion[] {
    if (!questions.length) return [];

    const validQuestions: SurveyQuestion[] = [];
    const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);
    let currentIndex = 0;

    while (currentIndex < sortedQuestions.length) {
      const currentQuestion = sortedQuestions[currentIndex];

      // Add current question to valid path
      validQuestions.push(currentQuestion);

      // Check if this question has conditional navigation
      if (currentQuestion.condition?.next) {
        const nextQuestionId = currentQuestion.condition.next(currentQuestion, sortedQuestions);

        if (nextQuestionId) {
          // Find the next question by ID
          const nextQuestionIndex = sortedQuestions.findIndex((q) => q.id === nextQuestionId);

          if (nextQuestionIndex >= 0) {
            currentIndex = nextQuestionIndex;
            continue;
          }
        }
      }

      // No conditional navigation, proceed to next question sequentially
      currentIndex++;
    }

    return validQuestions;
  }

  static createSurveyPayload(questions: SurveyQuestion[], seasonId: string) {
    // Only include questions that are part of the valid navigation path
    const validQuestions = this.getValidNavigationPath(questions);

    return validQuestions
      .map((question) => ({
        order: question.order,
        id: question.id,
        question: question.q,
        answer: this.processQuestionAnswer(question),
        season_id: seasonId,
      }))
      .filter(Boolean);
  }
}
