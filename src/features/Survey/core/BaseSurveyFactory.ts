import { SurveyQuestion } from '@types';
import { SurveyQuestionBuilder } from './SurveyQuestionBuilder';
import { BaseSurveyNavigator } from './BaseSurveyNavigator';

export interface QuestionConfig {
  order: number;
  id: string;
  title: string;
  subtitle?: string;
  type: SurveyQuestion['type'];
  options?: string[];
  textAreaOptions?: Array<{ key?: string; title?: string }>;
  ratingOptions?: { minKey: string; maxKey: string };
  multiple?: boolean;
  navigationCondition?: string;
  skipTo?: number;
}

export abstract class BaseSurveyFactory {
  protected navigator: BaseSurveyNavigator;

  constructor(
    protected t: (key: string) => string,
    navigator: BaseSurveyNavigator,
  ) {
    this.navigator = navigator;
  }

  protected createQuestionFromConfig(config: QuestionConfig): SurveyQuestion {
    const builder = new SurveyQuestionBuilder(this.t)
      .order(config.order)
      .id(config.id)
      .title(config.title)
      .type(config.type);

    if (config.subtitle) {
      builder.subtitle(config.subtitle);
    }

    if (config.multiple) {
      builder.multiple(config.multiple);
    }

    switch (config.type) {
      case 'select':
        if (config.options) {
          builder.options(config.options);
        } else if (config.textAreaOptions) {
          builder.textAreaOptions(config.textAreaOptions);
        }
        break;
      case 'rate':
        if (config.ratingOptions) {
          builder.rating(config.ratingOptions.minKey, config.ratingOptions.maxKey);
        }
        break;
      case 'area':
        builder.textArea();
        break;
    }

    if (config.navigationCondition) {
      const conditions = this.navigator.getNavigationConditions();
      const conditionFn = conditions[config.navigationCondition];
      if (conditionFn) {
        builder.condition(conditionFn);
      }
    }

    if (config.skipTo !== undefined) {
      builder.skipTo(config.skipTo);
    }

    return builder.build();
  }

  abstract getQuestionConfigs(): QuestionConfig[];

  createAllQuestions(): SurveyQuestion[] {
    return this.getQuestionConfigs()
      .map((config) => this.createQuestionFromConfig(config))
      .sort((a, b) => a.order - b.order);
  }
}
