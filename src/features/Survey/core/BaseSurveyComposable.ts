import { SurveyQuestion } from '@types';
import { SurveyNavigationHandler } from './SurveyNavigationHandler';
import { SurveyAnswerProcessor } from './SurveyAnswerProcessor';
import { BaseSurveyFactory } from './BaseSurveyFactory';

export function createBaseSurvey(surveyFactory: BaseSurveyFactory) {
  const currentQuestionIndex = ref(0);
  const questions: Ref<SurveyQuestion[]> = ref([]);

  const originalQuestions = computed(() => surveyFactory.createAllQuestions());

  function handleNextQuestion(questionIndex: number, updatedQuestions: SurveyQuestion[]): void {
    questions.value = updatedQuestions;
    currentQuestionIndex.value = SurveyNavigationHandler.getNextQuestionIndex(
      questionIndex,
      updatedQuestions,
    );
  }

  function handleBackQuestion(): void {
    currentQuestionIndex.value = SurveyNavigationHandler.getPreviousQuestionIndex(
      currentQuestionIndex.value,
      questions.value,
    );
  }

  function createSurveyPayload(surveyQuestions: SurveyQuestion[], seasonId: string) {
    return SurveyAnswerProcessor.createSurveyPayload(surveyQuestions, seasonId);
  }

  onMounted(() => {
    SurveyNavigationHandler.resetNavigationHistory();
  });

  return {
    currentQuestionIndex,
    questions,
    originalQuestions,
    handleNextQuestion,
    handleBackQuestion,
    createSurveyPayload,
  };
}
