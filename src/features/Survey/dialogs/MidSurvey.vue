<script setup lang="ts">
import { useUserStore } from '@stores';
import { useClaimMidSurveyRewardMutation, useUpdateMidSurveyMutation } from '@services';
import { SurveyQuestion } from '@types';
import { useFetchQueries } from '@composables';
import { SurveyQuestionItem } from '../components';
import { useMidSurvey } from '../composables';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const updateMidSurveyMutation = useUpdateMidSurveyMutation();
const claimMidSurveyRewardMutation = useClaimMidSurveyRewardMutation();
const storeUser = useUserStore();

const { currentSeason, gameSettings } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { userQuery } = useFetchQueries();
const {
  currentQuestionIndex,
  originalQuestions,
  handleNextQuestion,
  handleBackQuestion,
  createSurveyPayload,
} = useMidSurvey();

const loading = computed(() => {
  return updateMidSurveyMutation.isPending.value || claimMidSurveyRewardMutation.isPending.value;
});

async function handleSubmitSurvey(surveyQuestions: SurveyQuestion[]): Promise<void> {
  try {
    if (!currentSeason.value?.id) return;

    const data = createSurveyPayload(surveyQuestions, String(currentSeason.value.id));

    await updateMidSurveyMutation.mutateAsync({ data });
    await claimMidSurveyRewardMutation.mutateAsync();
    await userQuery.refetch();
    emits('close');
    openDialog('user_rewards', {
      crystal: gameSettings.value?.survey_crystal_reward,
    });
  } catch (error) {
    console.error('Error submitting survey:', error);
  }
}
</script>

<template>
  <Dialog @close="emits('close')">
    <template #btnTopLeft v-if="currentQuestionIndex > 0">
      <Button
        track-id="disable-track"
        shape="square"
        variant="secondary"
        @click="handleBackQuestion"
        :class="{
          'pointer-events-none': loading,
        }"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div>{{ t('SURVEY_POPUP_HEADING') }}</div>
    </template>

    <SurveyQuestionItem
      :loading="loading"
      :questions="originalQuestions"
      :initialQuestionIndex="currentQuestionIndex"
      @next="handleNextQuestion"
      @completed="handleSubmitSurvey"
    />
  </Dialog>
</template>
