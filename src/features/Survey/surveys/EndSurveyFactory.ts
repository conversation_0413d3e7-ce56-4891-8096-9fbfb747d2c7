import { BaseSurveyFactory, QuestionConfig, BaseSurveyNavigator } from '../core';
import { SurveyQuestion } from '@types';

export class EndSurveyNavigator extends BaseSurveyNavigator {
  getNavigationConditions() {
    return {
      whichCoinHuntCondition: this.whichCoinHuntCondition,
      notHuntCondition: this.notHuntCondition,
      huntingExperienceCondition: this.huntingExperienceCondition,
      usefulHintsCondition: this.usefulHintsCondition,
      crystalActionsCondition: this.crystalActionsCondition,
      powerupUsefulCondition: this.powerupUsefulCondition,
      skipToReccHTM: this.skipTo('END_SURVEYQUESTION_RECC_HTM'),
      skipToOtherFeedback: this.skipTo('END_SURVEYQUESTION_OTHER_FEEDBACK'),
      skipToPowerupUseful: this.skipTo('END_SURVEYQUESTION_POWERUP_USEFUL'),
    };
  }

  // Navigation condition for question: Which coin hunt
  private whichCoinHuntCondition = (currentQuestion?: SurveyQuestion): string => {
    const hunt4Selected = currentQuestion?.a.some(
      (a) => a.selected && a.value === this.t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_4'),
    );

    if (hunt4Selected) return this.getQuestionId('END_SURVEYQUESTION_NOTHUNT');
    return this.getQuestionId('END_SURVEYQUESTION_HUNTING_EXPERIENCE');
  };

  // Navigation condition for question: Not hunt
  private notHuntCondition = (currentQuestion?: SurveyQuestion): string => {
    const hunt56Selected = currentQuestion?.a.some(
      (a) =>
        a.selected &&
        (a.value === this.t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_1') ||
          a.value === this.t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_2')),
    );
    if (hunt56Selected) return this.getQuestionId('END_SURVEYQUESTION_HUNTING_EXPERIENCE');
    return this.getQuestionId('END_SURVEYQUESTION_RECC_HTM');
  };

  // Navigation condition for question: Hunting experience
  private huntingExperienceCondition = (currentQuestion?: SurveyQuestion): string => {
    if (!currentQuestion) return this.getQuestionId('END_SURVEYQUESTION_ENJOY_HTM');
    const ratingValue = Number(currentQuestion.a[0]?.value || 0);
    return ratingValue > 6
      ? this.getQuestionId('END_SURVEYQUESTION_ENJOY_HTM')
      : this.getQuestionId('END_SURVEYQUESTION_NOTENJOY_HTM');
  };

  // Navigation condition for question: Useful hints
  private usefulHintsCondition = (currentQuestion?: SurveyQuestion): string => {
    if (!currentQuestion) return this.getQuestionId('END_SURVEYQUESTION_CRYSTAL_ACTIONS');
    const ratingValue = Number(currentQuestion.a[0]?.value || 0);
    return ratingValue > 6
      ? this.getQuestionId('END_SURVEYQUESTION_CRYSTAL_ACTIONS')
      : this.getQuestionId('END_SURVEYQUESTION_HINTS_FEEDBACK');
  };

  // Navigation condition for question: Crystal actions
  private crystalActionsCondition = (currentQuestion?: SurveyQuestion): string => {
    if (!currentQuestion) return this.getQuestionId('END_SURVEYQUESTION_WHY_NO_CRYSTAL_ACTIONS');
    const selectedOption = currentQuestion.a.find(
      (item) => item.selected && item.value === this.t('END_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1'),
    );
    if (selectedOption) return this.getQuestionId('END_SURVEYQUESTION_WHY_CRYSTAL_ACTIONS');
    return this.getQuestionId('END_SURVEYQUESTION_WHY_NO_CRYSTAL_ACTIONS');
  };

  // Navigation condition for question: Powerup useful
  private powerupUsefulCondition = (currentQuestion?: SurveyQuestion): string => {
    if (!currentQuestion) return this.getQuestionId('END_SURVEYQUESTION_POWERUP_APPEALING');
    const selectedOption = currentQuestion.a.find(
      (item) => item.selected && item.value === this.t('END_SURVEY_OPTIONS_POWERUP_USEFUL_5'),
    );
    if (selectedOption) return this.getQuestionId('END_SURVEYQUESTION_NOT_USE_POWERUP');
    return this.getQuestionId('END_SURVEYQUESTION_POWERUP_APPEALING');
  };
}

export class EndSurveyFactory extends BaseSurveyFactory {
  constructor(t: (key: string) => string) {
    super(t, new EndSurveyNavigator(t));
  }

  getQuestionConfigs(): QuestionConfig[] {
    return [
      {
        order: 1,
        id: 'END_SURVEYQUESTION_AGE',
        title: 'END_SURVEYQUESTION_AGE',
        subtitle: 'END_SURVEY_DESC_AGE',
        type: 'select',
        options: [
          'END_SURVEY_OPTIONS_AGE_1',
          'END_SURVEY_OPTIONS_AGE_2',
          'END_SURVEY_OPTIONS_AGE_3',
          'END_SURVEY_OPTIONS_AGE_4',
          'END_SURVEY_OPTIONS_AGE_5',
          'END_SURVEY_OPTIONS_AGE_6',
          'END_SURVEY_OPTIONS_AGE_7',
          'END_SURVEY_OPTIONS_AGE_8',
        ],
      },
      {
        order: 2,
        id: 'END_SURVEYQUESTION_GENDER',
        title: 'END_SURVEYQUESTION_GENDER',
        subtitle: 'END_SURVEY_DESC_GENDER',
        type: 'select',
        options: ['END_SURVEY_OPTIONS_GENDER_1', 'END_SURVEY_OPTIONS_GENDER_2'],
      },
      {
        order: 3,
        id: 'END_SURVEYQUESTION_FINDOUTHTM',
        title: 'END_SURVEYQUESTION_FINDOUTHTM',
        subtitle: 'END_SURVEYDESC_FINDOUTHTM',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { title: 'END_SURVEY_OPTIONS_FINDOUTHTM_1' },
          { title: 'END_SURVEY_OPTIONS_FINDOUTHTM_2' },
          { key: 'END_SURVEY_OPTIONS_FINDOUTHTM_3' },
          { key: 'END_SURVEY_OPTIONS_FINDOUTHTM_4' },
          { key: 'END_SURVEY_OPTIONS_FINDOUTHTM_5' },
          { key: 'END_SURVEY_OPTIONS_FINDOUTHTM_6' },
          { key: 'END_SURVEY_OPTIONS_FINDOUTHTM_7' },
          { title: 'END_SURVEY_OPTIONS_FINDOUTHTM_8' },
        ],
      },
      {
        order: 4,
        id: 'END_SURVEYQUESTION_WHICH_COIN_HUNT',
        title: 'END_SURVEYQUESTION_WHICH_COIN_HUNT',
        subtitle: 'END_SURVEY_DESC_WHICH_COIN_HUNT',
        type: 'select',
        multiple: true,
        options: [
          'END_SURVEY_OPTIONS_WHICH_COIN_HUNT_1',
          'END_SURVEY_OPTIONS_WHICH_COIN_HUNT_2',
          'END_SURVEY_OPTIONS_WHICH_COIN_HUNT_3',
          'END_SURVEY_OPTIONS_WHICH_COIN_HUNT_4',
        ],
        navigationCondition: 'whichCoinHuntCondition',
      },
      {
        order: 5,
        id: 'END_SURVEYQUESTION_NOTHUNT',
        title: 'END_SURVEYQUESTION_NOTHUNT',
        subtitle: 'END_SURVEYDESC_NOTHUNT',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'END_SURVEY_OPTIONS_NOTHUNT_1' },
          { key: 'END_SURVEY_OPTIONS_NOTHUNT_2' },
          { key: 'END_SURVEY_OPTIONS_NOTHUNT_3' },
          { key: 'END_SURVEY_OPTIONS_NOTHUNT_4' },
          { title: 'END_SURVEY_OPTIONS_NOTHUNT_5' },
        ],
        navigationCondition: 'notHuntCondition',
      },
      {
        order: 6,
        id: 'END_SURVEYQUESTION_HUNTING_EXPERIENCE',
        title: 'END_SURVEYQUESTION_HUNTING_EXPERIENCE',
        subtitle: 'END_SURVEYDESC_HUNTING_EXPERIENCE',
        type: 'rate',
        ratingOptions: {
          minKey: 'END_SURVEY_OPTIONS_HUNTING_EXPERIENCE_RATE_1',
          maxKey: 'END_SURVEY_OPTIONS_HUNTING_EXPERIENCE_RATE_2',
        },
        navigationCondition: 'huntingExperienceCondition',
      },
      {
        order: 7,
        id: 'END_SURVEYQUESTION_ENJOY_HTM',
        title: 'END_SURVEYQUESTION_ENJOY_HTM',
        subtitle: 'END_SURVEYDESC_ENJOY_HTM',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'END_SURVEY_OPTIONS_ENJOY_HTM_1' },
          { key: 'END_SURVEY_OPTIONS_ENJOY_HTM_2' },
          { key: 'END_SURVEY_OPTIONS_ENJOY_HTM_3' },
          { key: 'END_SURVEY_OPTIONS_ENJOY_HTM_4' },
          { title: 'END_SURVEY_OPTIONS_ENJOY_HTM_5' },
        ],
        navigationCondition: 'skipToReccHTM',
      },
      {
        order: 8,
        id: 'END_SURVEYQUESTION_NOTENJOY_HTM',
        title: 'END_SURVEYQUESTION_NOTENJOY_HTM',
        subtitle: 'END_SURVEYDESC_NOTENJOY_HTM',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'END_SURVEY_OPTIONS_NOTENJOY_HTM_1' },
          { key: 'END_SURVEY_OPTIONS_NOTENJOY_HTM_2' },
          { key: 'END_SURVEY_OPTIONS_NOTENJOY_HTM_3' },
          { title: 'END_SURVEY_OPTIONS_NOTENJOY_HTM_4' },
        ],
      },
      {
        order: 9,
        id: 'END_SURVEYQUESTION_RECC_HTM',
        title: 'END_SURVEYQUESTION_RECC_HTM',
        subtitle: 'END_SURVEYDESC_RECC_HTM',
        type: 'rate',
        ratingOptions: {
          minKey: 'END_SURVEY_OPTIONS_RECC_HTM_1',
          maxKey: 'END_SURVEY_OPTIONS_RECC_HTM_2',
        },
      },
      {
        order: 10,
        id: 'END_SURVEYQUESTION_SENTOSA_CHECKIN',
        title: 'END_SURVEYQUESTION_SENTOSA_CHECKIN',
        subtitle: 'END_SURVEYDESC_SENTOSA_CHECKIN',
        type: 'rate',
        ratingOptions: {
          minKey: 'END_SURVEY_OPTIONS_SENTOSA_CHECKIN_1',
          maxKey: 'END_SURVEY_OPTIONS_SENTOSA_CHECKIN_2',
        },
      },
      {
        order: 11,
        id: 'END_SURVEYQUESTION_ATTRACTIVE_DISCOVERY',
        title: 'END_SURVEYQUESTION_ATTRACTIVE_DISCOVERY',
        subtitle: 'END_SURVEYDESC_ATTRACTIVE_DISCOVERY',
        type: 'rate',
        ratingOptions: {
          minKey: 'END_SURVEY_OPTIONS_ATTRACTIVE_DISCOVERY_1',
          maxKey: 'END_SURVEY_OPTIONS_ATTRACTIVE_DISCOVERY_2',
        },
      },
      {
        order: 12,
        id: 'END_SURVEYQUESTION_USEFUL_HINTS',
        title: 'END_SURVEYQUESTION_USEFUL_HINTS',
        subtitle: 'END_SURVEYDESC_USEFUL_HINTS',
        type: 'rate',
        ratingOptions: {
          minKey: 'END_SURVEY_OPTIONS_USEFUL_HINTS_1',
          maxKey: 'END_SURVEY_OPTIONS_USEFUL_HINTS_2',
        },
        navigationCondition: 'usefulHintsCondition',
      },
      {
        order: 13,
        id: 'END_SURVEYQUESTION_HINTS_FEEDBACK',
        title: 'END_SURVEYQUESTION_HINTS_FEEDBACK',
        subtitle: 'END_SURVEYDESC_HINTS_FEEDBACK',
        type: 'area',
      },
      {
        order: 14,
        id: 'END_SURVEYQUESTION_CRYSTAL_ACTIONS',
        title: 'END_SURVEYQUESTION_CRYSTAL_ACTIONS',
        subtitle: 'END_SURVEYDESC_CRYSTAL_ACTIONS',
        type: 'select',
        options: ['END_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1', 'END_SURVEY_OPTIONS_CRYSTAL_ACTIONS_2'],
        navigationCondition: 'crystalActionsCondition',
      },
      {
        order: 15,
        id: 'END_SURVEYQUESTION_WHY_CRYSTAL_ACTIONS',
        title: 'END_SURVEYQUESTION_WHY_CRYSTAL_ACTIONS',
        subtitle: 'END_SURVEYDESC_WHY_CRYSTAL_ACTIONS',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'END_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_1' },
          { key: 'END_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_2' },
          { key: 'END_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_3' },
          { title: 'END_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_4' },
        ],
        navigationCondition: 'skipToPowerupUseful',
      },
      {
        order: 16,
        id: 'END_SURVEYQUESTION_WHY_NO_CRYSTAL_ACTIONS',
        title: 'END_SURVEYQUESTION_WHY_NO_CRYSTAL_ACTIONS',
        subtitle: 'SURVEYDESC_WHY_NO_CRYSTAL_ACTIONS',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'END_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_1' },
          { key: 'END_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_2' },
          { key: 'END_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_3' },
          { title: 'END_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_4' },
        ],
      },
      {
        order: 17,
        id: 'END_SURVEYQUESTION_POWERUP_USEFUL',
        title: 'END_SURVEYQUESTION_POWERUP_USEFUL',
        subtitle: 'END_SURVEY_DESC_POWERUP_USEFUL',
        type: 'select',
        options: [
          'END_SURVEY_OPTIONS_POWERUP_USEFUL_1',
          'END_SURVEY_OPTIONS_POWERUP_USEFUL_2',
          'END_SURVEY_OPTIONS_POWERUP_USEFUL_3',
          'END_SURVEY_OPTIONS_POWERUP_USEFUL_4',
          'END_SURVEY_OPTIONS_POWERUP_USEFUL_5',
        ],
        navigationCondition: 'powerupUsefulCondition',
      },
      {
        order: 18,
        id: 'END_SURVEYQUESTION_POWERUP_APPEALING',
        title: 'END_SURVEYQUESTION_POWERUP_APPEALING',
        subtitle: 'END_SURVEY_DESC_POWERUP_APPEALING',
        type: 'select',
        options: [
          'END_SURVEY_OPTIONS_POWERUP_APPEALING_1',
          'END_SURVEY_OPTIONS_POWERUP_APPEALING_2',
          'END_SURVEY_OPTIONS_POWERUP_APPEALING_3',
          'END_SURVEY_OPTIONS_POWERUP_APPEALING_4',
        ],
        navigationCondition: 'skipToOtherFeedback',
      },
      {
        order: 19,
        id: 'END_SURVEYQUESTION_NOT_USE_POWERUP',
        title: 'END_SURVEYQUESTION_NOT_USE_POWERUP',
        subtitle: 'END_SURVEY_DESC_NOT_USE_POWERUP',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'END_SURVEY_OPTIONS_NOT_USE_POWERUP_1' },
          { key: 'END_SURVEY_OPTIONS_NOT_USE_POWERUP_2' },
          { key: 'END_SURVEY_OPTIONS_NOT_USE_POWERUP_3' },
          { title: 'END_SURVEY_OPTIONS_NOT_USE_POWERUP_4' },
        ],
      },
      {
        order: 20,
        id: 'END_SURVEYQUESTION_OTHER_FEEDBACK',
        title: 'END_SURVEYQUESTION_OTHER_FEEDBACK',
        subtitle: 'END_SURVEYDESC_OTHER_FEEDBACK',
        type: 'area',
      },
    ];
  }
}
