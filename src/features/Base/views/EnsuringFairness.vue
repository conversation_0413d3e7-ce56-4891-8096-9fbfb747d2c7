<script lang="ts" setup>
import { CarAnimation } from '@components';
import { useUserStore } from '@stores';

const storeUser = useUserStore();

const { seasonCode } = storeToRefs(storeUser);
const { t } = useI18n();
const { push, openDialog } = useMicroRoute();

const CONTENTS = [
  {
    header: t('ENSURINGFAIRNESS_1_TITLE'),
    content: t('ENSURINGFAIRNESS_1_BODY'),
  },
  {
    header: t('ENSURINGFAIRNESS_2_TITLE'),
    content: t('ENSURINGFAIRNESS_2_BODY'),
  },
  {
    header: t('ENSURINGFAIRNESS_3_TITLE'),
    content: t('ENSURINGFAIRNESS_3_BODY'),
  },
  {
    header: t('ENSURINGFAIRNESS_4_TITLE'),
    content: t('ENSURINGFAIRNESS_4_BODY'),
  },
];

function handleClick(e: Event): void {
  const target = e.target as HTMLElement;
  if (target.id === 'goTAC') {
    openDialog('tac');
  }
}

onMounted(async () => {
  await nextTick();
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <div class="fit ensuring-fairness" :class="`season-${seasonCode.toLowerCase()}`">
    <Button
      track-id="disable-track"
      class="fixed top-2 left-2"
      shape="square"
      variant="secondary"
      @click="push(-1)"
    >
      <Icon name="arrow-left" />
    </Button>
    <div class="header fixed font-bold text-lg mt-2" v-html="t('MENU_FAIRNESS_HEADER')"></div>
    <CarAnimation assets="sov/van/sqkii" />

    <div class="text-center italic mb-[30px] mt-5" v-html="t('ENSURINGFAIRNESS_DESC')"></div>
    <div class="ensuring-fairness__content">
      <Expansion v-for="content in CONTENTS" :key="content.header" group="ensuring">
        <template v-slot:header>
          <div class="text-xl font-bold w-[90%]" v-html="content.header"></div>
        </template>
        <q-card style="background: transparent">
          <q-card-section>
            <div class="text-sm" v-html="content.content"></div>
          </q-card-section>
        </q-card>
      </Expansion>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ensuring-fairness {
  display: flex;
  flex-flow: column;
  padding: 100% 10px 20px;
  background-color: #0f132a !important;

  &.season-sg {
    background: url('/imgs/kv/sg.png') center -10vw no-repeat;
    background-size: 100% 112vw;
  }
  &.season-vn {
    background-size: 100% 112vw !important;
    background: url('/imgs/kv/vn.png') center -10vw no-repeat;
  }

  &__content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .header {
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
