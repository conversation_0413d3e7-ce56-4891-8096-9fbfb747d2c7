<script setup lang="ts">
import { useUserStore } from '@stores';
import { useFAQ } from '../composables';
import { createFAQSections } from '../utils';

const storeUser = useUserStore();

const { initialSlide } = storeToRefs(storeUser);
const { t } = useI18n();
const { push, openDialog } = useMicroRoute();

const faqSections = computed(() => createFAQSections(t));
const { search, selectedCategory, categoryOptions, filteredSections } = useFAQ({
  sections: faqSections,
});

function handleGoBack(): void {
  push(-1);
}

function handleTACClick(): void {
  openDialog('tac');
}

function handleTimelineClick(): void {
  openDialog('timeline_logs', {
    defaultInitialSlide: initialSlide.value + 1,
  });
}

function handleCrystalsClick(): void {
  push('offer_wall');
}

function handleClick(e: Event): void {
  const target = e.target as HTMLElement;
  switch (target.id) {
    case 'TAC':
      handleTACClick();
      break;
    case 'goTimeline':
      handleTimelineClick();
      break;
    case 'crystals':
      handleCrystalsClick();
      break;
    default:
      break;
  }
}

onMounted(async () => {
  await nextTick();
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <div class="faq">
    <Button
      track-id="disable-track"
      shape="square"
      class="mb-[25px]"
      variant="secondary"
      @click="handleGoBack"
    >
      <Icon name="arrow-left" />
    </Button>
    <div
      class="absolute text-center top-4 left-1/2 -translate-x-1/2 text-lg font-bold"
      v-html="t('FAQ_HEADING')"
    />

    <div class="px-3 py-5">
      <Input
        v-model="search"
        class="full-width input-search mb-5"
        type="search"
        :label="t('FAQ_SEARCH')"
      />
      <Select
        v-model="selectedCategory"
        class="mb-4"
        :label="t('FAQ_ALLCATEGORIES')"
        :options="categoryOptions"
        type="secondary"
      />
    </div>

    <div class="faq-content">
      <Expansion
        v-for="{ header, list } in filteredSections"
        :key="header"
        switch-toggle-side
        expand-separator
        group="faq"
      >
        <template #header>
          <div class="text-xl font-bold w-[90%]" v-html="header" />
        </template>
        <div class="px-5">
          <div v-for="{ question, answer } in list" :key="question" class="faq-card">
            <div class="text-lg font-semibold mb-2" v-html="question" />
            <div class="text-sm" v-html="answer" />
          </div>
        </div>
      </Expansion>
    </div>
  </div>
</template>

<style lang="scss">
.faq {
  position: relative;
  background-color: #090422;
  padding: 10px;
  display: flex;
  flex-flow: column;
  width: 100%;
  height: 100%;
  .select-search {
    .q-field__label {
      color: #ffffff;
      opacity: 0.5;
    }
    .q-field__inner {
      z-index: 9;
      background: #2e3b54;
      border-radius: 4px;
    }

    .q-field__native {
      color: #ffffff;
    }
    .q-chip {
      background: #6e60cb;
      color: #ffffff;
    }
  }

  &-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    .faq-card {
      background: linear-gradient(
        178.55deg,
        rgba(37, 25, 109, 0.95) 1.24%,
        rgba(29, 65, 137, 0.9) 46.04%
      );
      border-radius: 4px;
      padding: 20px 12px;
      &:not(:last-child) {
        margin-bottom: 20px;
      }
    }
    li {
      margin-bottom: 10px;
    }
  }
}
</style>
