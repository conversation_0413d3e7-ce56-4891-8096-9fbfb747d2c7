<script lang="ts" setup>
import { useTimelineQuery } from '@services';
import { useUserStore } from '@stores';
import { TimelineLogs } from '@types';

interface Emits {
  (e: 'close'): void;
}

interface AdventureLogs {
  ongoing: TimelineLogs[];
  past: TimelineLogs[];
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { timelineLogs, currentSeason } = storeToRefs(storeUser);
const { openDialog } = useMicroRoute();
const { t } = useI18n();

const timelineQuery = useTimelineQuery({
  select: (data) => {
    storeUser.setTimelineLogs(data);
    return data;
  },
});

const isLoading = computed(() => timelineQuery.isLoading.value);

const adventureLogs = computed<AdventureLogs>(() => {
  const logs = timelineLogs.value;
  return {
    ongoing: logs.filter((t) => t.status === 'ongoing').reverse(),
    past: logs.filter((t) => t.status === 'ended').reverse(),
  };
});

function openTimelineDialog(tl?: TimelineLogs): void {
  if (tl) {
    const index = timelineLogs.value.findIndex((t) => t._id === tl._id);
    openDialog('timeline_logs', { defaultInitialSlide: index });
  } else openDialog('timeline_logs');
  emits('close');
}
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('ADVENTURELOG_POPUP_HEADING')"></div>
    </template>
    <div class="relative">
      <div
        class="text-center text-base font-bold mb-1"
        v-html="t('ADVENTURELOG_POPUP_DESCRIPTION_1')"
      ></div>
      <div class="text-center text-sm" v-html="t('ADVENTURELOG_POPUP_DESCRIPTION_2')"></div>

      <!-- Loading Skeleton -->
      <template v-if="isLoading">
        <!-- Ongoing Adventure Logs Skeleton -->
        <div class="flex flex-col gap-2">
          <div
            class="card-log current mt-2 skeleton-card"
            v-for="i in 2"
            :key="`ongoing-skeleton-${i}`"
          >
            <div class="skeleton-text skeleton-text--date"></div>
            <div class="skeleton-text skeleton-text--title"></div>
            <div class="skeleton-text skeleton-text--content"></div>
            <div class="skeleton-text skeleton-text--content skeleton-text--short"></div>
          </div>
        </div>

        <!-- Past Adventure Logs Skeleton -->

        <div class="flex flex-col gap-2 mt-2">
          <div class="card-log skeleton-card" v-for="i in 4" :key="`past-skeleton-${i}`">
            <div class="skeleton-icon skeleton-icon--location"></div>
            <div class="skeleton-text skeleton-text--date"></div>
            <div class="skeleton-text skeleton-text--title"></div>
            <div class="skeleton-text skeleton-text--content"></div>
            <div class="skeleton-text skeleton-text--content skeleton-text--short"></div>
          </div>
        </div>

        <!-- More Hunts Link Skeleton -->
        <div class="mt-3 text-center">
          <div class="skeleton-text skeleton-text--link"></div>
        </div>
      </template>

      <!-- Actual Content -->
      <template v-else>
        <template v-if="adventureLogs.ongoing.length">
          <div class="mt-10 opacity-80 ml-2 text-sm" v-html="t('ADVENTURELOG_POPUP_CURRENT')"></div>
          <div class="flex flex-col gap-12">
            <div
              class="card-log current mt-2"
              v-for="tl in adventureLogs.ongoing"
              :key="tl._id"
              v-tracker="{
                id: 'adventure_logs',
                data: {
                  adventure_log_id: tl._id,
                  adventure_log_city: tl.city,
                },
              }"
              @click="openTimelineDialog(tl)"
            >
              <Icon class="sqkii-smile pointer-events-none" name="sqkii-smile" />
              <Icon class="absolute top-3 right-3" name="current-location" />

              <div class="text-sm" v-html="t(tl.adventure_log_date)"></div>
              <div class="text-sm font-bold" v-html="t(tl.adventure_log_title)"></div>
              <div class="text-sm" v-html="t(tl.adventure_log_content)"></div>
            </div>
          </div>
        </template>

        <div class="mt-3 opacity-80 ml-3 text-sm" v-html="t('ADVENTURELOG_POPUP_COMPLETED')"></div>
        <div
          class="card-log mt-2"
          v-for="(tl, index) in adventureLogs.past"
          :key="tl._id"
          v-tracker="{
            id: 'adventure_logs',
            data: {
              adventure_log_id: tl._id,
              adventure_log_city: tl.city,
            },
          }"
          @click="openTimelineDialog(tl)"
        >
          <Icon
            v-if="index === 0 && currentSeason?.status === 'ended'"
            name="sqkii-smile"
            class="absolute w-[50px] right-0 -top-[45px] pointer-events-none"
          />
          <Icon class="absolute top-3 right-3" name="location" />
          <div class="text-sm" v-html="t(tl.adventure_log_date)"></div>
          <div class="text-sm font-bold" v-html="t(tl.adventure_log_title)"></div>
          <div class="text-sm" v-html="t(tl.adventure_log_content)"></div>
        </div>
        <div
          class="underline text-link mt-3 text-center"
          v-html="t('ADVENTURELOG_POPUP_MOREHUNTS')"
          v-tracker="{
            id: 'adventure_logs',
            data: {
              target: 'more_hunts',
            },
          }"
          @click="openTimelineDialog()"
        ></div>
      </template>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>
.card-log {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 12px;
  border-radius: 4px;
  background: #091a3c;
  &.current {
    background: linear-gradient(180deg, rgba(147, 75, 218, 0.8) 0%, #511d85 100%);
  }

  & .sqkii-smile {
    position: absolute;
    right: 0px;
    top: -45px;
    width: 50px;
    height: 45px;
  }
}

// Skeleton Loading Styles
.skeleton-card {
  pointer-events: none;
  cursor: default;
}

.skeleton-text {
  background: linear-gradient(90deg, #2a3f84 25%, #3a4f94 50%, #2a3f84 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;

  &--section-header {
    height: 16px;
    width: 120px;
  }

  &--date {
    height: 14px;
    width: 100px;
  }

  &--title {
    height: 16px;
    width: 180px;
  }

  &--content {
    height: 14px;
    width: 100%;

    &.skeleton-text--short {
      width: 60%;
      margin-bottom: 0;
    }
  }

  &--link {
    height: 16px;
    width: 140px;
    margin: 0 auto;
  }
}

.skeleton-icon {
  background: linear-gradient(90deg, #2a3f84 25%, #3a4f94 50%, #2a3f84 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  position: absolute;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
