import { FAQSection } from '@types';

export function createFAQSections(t: (key: string) => string): FAQSection[] {
  return [
    {
      header: t('FAQ_ABOUTTHEGAME'),
      list: [
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION1_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION6_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION6_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION2_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION3_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION3_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION4_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION4_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION5_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION5_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_ACCOUNTCREATION'),
      list: [
        {
          question: t('FAQ_ACCOUNTCREATION_QUESTION1_HEADING'),
          answer: t('FAQ_ACCOUNTCREATION_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_ACCOUNTCREATION_QUESTION2_HEADING'),
          answer: t('FAQ_ACCOUNTCREATION_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_ACCOUNTCREATION_QUESTION3_HEADING'),
          answer: t('FAQ_ACCOUNTCREATION_QUESTION3_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_HOWTOHUNT'),
      list: [
        {
          question: t('FAQ_HOWTOHUNT_QUESTION1_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION2_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION5_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION5_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION3_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION3_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION4_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION4_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION6_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION6_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION7_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION7_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION8_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION8_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_GETMORE'),
      list: [
        {
          question: t('FAQ_GETMORE_QUESTION1_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_GETMORE_QUESTION2_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_GETMORE_QUESTION3_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION3_DESCRIPTION'),
        },
        {
          question: t('FAQ_GETMORE_QUESTION4_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION4_DESCRIPTION'),
        },
        {
          question: t('FAQ_GETMORE_QUESTION5_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION5_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_REFERRAL'),
      list: [
        {
          question: t('FAQ_REFERRAL_QUESTION1_HEADING'),
          answer: t('FAQ_REFERRAL_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_REFERRAL_QUESTION2_HEADING'),
          answer: t('FAQ_REFERRAL_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_REFERRAL_QUESTION3_HEADING'),
          answer: t('FAQ_REFERRAL_QUESTION3_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_SUPPORT'),
      list: [
        {
          question: t('FAQ_SUPPORT_QUESTION1_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION2_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION3_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION3_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION4_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION4_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION5_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION5_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION6_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION6_DESCRIPTION'),
        },
      ],
    },
  ];
}
