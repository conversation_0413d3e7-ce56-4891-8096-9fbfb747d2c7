import { FAQSection, UseFAQOptions, UseFAQReturn } from '@types';

export function useFAQ({ sections }: UseFAQOptions): UseFAQReturn {
  const search = ref('');
  const selectedCategory = ref('');

  const categoryOptions = computed<string[]>(() => sections.value.map((section) => section.header));

  const filteredByCategory = computed<FAQSection[]>(() => {
    if (!selectedCategory.value) {
      return sections.value;
    }
    return sections.value.filter((section) => section.header === selectedCategory.value);
  });

  const filteredSections = computed<FAQSection[]>(() => {
    if (!search.value.trim()) {
      return filteredByCategory.value;
    }

    const searchRegex = new RegExp(search.value.trim(), 'i');

    return filteredByCategory.value
      .map((section) => ({
        ...section,
        list: section.list.filter(
          (item) => searchRegex.test(item.question) || searchRegex.test(item.answer),
        ),
      }))
      .filter((section) => searchRegex.test(section.header) || section.list.length > 0);
  });

  function clearFilters(): void {
    search.value = '';
    selectedCategory.value = '';
  }

  return {
    search,
    selectedCategory,
    categoryOptions,
    filteredSections,
    clearFilters,
  };
}
