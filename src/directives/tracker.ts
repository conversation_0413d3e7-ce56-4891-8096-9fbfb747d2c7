import { Directive, DirectiveBinding } from 'vue';
import { usePageTracker } from '@composables';

interface TrackerBinding {
  id?: string;
  data?: Record<string, any>;
  throttle?: number;
}

interface TrackerElement extends HTMLElement {
  _trackerCleanup?: () => void;
  _trackerThrottleTimer?: number | undefined;
}

const pageTracker = usePageTracker();

function throttle<T extends (...args: any[]) => void>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: number | undefined;
  return (...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = window.setTimeout(() => func(...args), delay);
  };
}

function handleTracking(binding: TrackerBinding, element?: Element): () => void {
  const { id, data = {}, throttle: throttleMs = 0 } = binding;

  function trackEvent(): void {
    const trackingId = id || element?.getAttribute('data-track-id') || 'disable-track';
    if (trackingId === 'disable-track') return;

    try {
      if (!pageTracker || typeof pageTracker.tracker !== 'function') {
        return;
      }

      pageTracker.tracker({
        id: trackingId,
        action: 'click',
        data: {
          element_tag: element?.tagName.toLowerCase(),
          ...data,
        },
      });
    } catch (error) {
      console.error('❌ Tracking error:', error);
    }
  }

  return throttleMs > 0 ? throttle(trackEvent, throttleMs) : trackEvent;
}

function setupClickTracking(el: TrackerElement, binding: TrackerBinding): void {
  const handler = handleTracking(binding, el);

  // Use capture phase to ensure tracking happens before other click handlers
  el.addEventListener('click', handler, { capture: true });

  el._trackerCleanup = () => {
    el.removeEventListener('click', handler, { capture: true });
  };
}

export const vTracker: Directive<TrackerElement, TrackerBinding | string> = {
  mounted(el: TrackerElement, binding: DirectiveBinding<TrackerBinding | string>) {
    const config: TrackerBinding =
      typeof binding.value === 'string' ? { id: binding.value } : binding.value || {};

    setupClickTracking(el, config);
  },

  updated(el: TrackerElement, binding: DirectiveBinding<TrackerBinding | string>) {
    // Clean up old listeners
    if (el._trackerCleanup) {
      el._trackerCleanup();
    }

    // Re-setup with new binding
    const config: TrackerBinding =
      typeof binding.value === 'string' ? { id: binding.value } : binding.value || {};

    setupClickTracking(el, config);
  },

  beforeUnmount(el: TrackerElement) {
    if (el._trackerCleanup) {
      el._trackerCleanup();
    }

    if (el._trackerThrottleTimer) {
      clearTimeout(el._trackerThrottleTimer);
    }
  },
};
