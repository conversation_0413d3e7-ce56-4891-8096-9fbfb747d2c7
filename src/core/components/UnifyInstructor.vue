<script lang="ts" setup>
import { useDialogStore } from '@stores';
import { storeToRefs } from 'pinia';

const storeDialog = useDialogStore();

const { showInstructor, instructorData } = storeToRefs(storeDialog);
</script>
<template>
  <TimiiInstructor v-if="showInstructor === 'timii'" v-bind="{ ...instructorData }" />
  <SqkiiInstructor v-if="showInstructor === 'sqkii'" v-bind="{ ...instructorData }" />
  <NanciiInstructor v-if="showInstructor === 'nancii'" v-bind="{ ...instructorData }" />
  <ShinobiiInstructor v-if="showInstructor === 'shinobii'" v-bind="{ ...instructorData }" />
  <RMISqkiiInstructor v-if="showInstructor === 'rmi'" v-bind="{ ...instructorData }" />
</template>
