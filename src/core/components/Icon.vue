<script setup lang="ts">
interface Props {
  /**
   * The name of the asset to be displayed.
   * This can be a file name without extension for images or a URL for external assets.
   */
  name: string;
  /**
   * The size of the icon in pixels.
   * Default is 18 pixels.
   */
  size?: number;
  /**
   * The type of asset to be displayed.
   * Options include 'png', 'svg', 'gif', or 'url' for external links.
   * Default is 'png'.
   */
  type?: 'png' | 'svg' | 'gif' | 'url';
  /**
   * Whether to load the image lazily.
   * If true, the image will only load when it is in the viewport.
   * Default is false.
   */
  lazy?: boolean;
}

interface Emits {
  (e: 'click'): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: 18,
  type: 'png',
});

const emits = defineEmits<Emits>();

const assetUrl = computed(() => {
  const url = props.type === 'url' ? props.name : `/imgs/${props.name}.${props.type}`;
  return url.replace(/([^:])\/+/g, '$1/');
});
</script>

<template>
  <q-img
    v-if="props.lazy"
    v-bind="$attrs"
    :alt="name"
    :src="assetUrl"
    :class="`!w-[${size}px]`"
    :width="`${size}px`"
    @click="emits('click')"
    style="max-width: unset"
  />
  <img
    v-else
    v-bind="$attrs"
    :alt="name"
    :src="assetUrl"
    :class="`!w-[${size}px]`"
    :width="`${size}px`"
    @click="emits('click')"
    style="max-width: unset"
  />
</template>
