<script lang="ts" setup>
type SelectType = 'primary' | 'secondary' | 'neon-outline';

interface SelectOption {
  label: string;
  value: any;
  disable?: boolean;
}

interface Props {
  /**
   * Type of the select component, which determines its style.
   * Options include 'primary', 'secondary', and 'neon-outline'.
   */
  type?: SelectType;
  /**
   * Model value for the select component.
   * This is the value bound to the select field.
   */
  modelValue: any;
  /**
   * Label for the select field.
   * This will be displayed above the select input.
   */
  label: string;
  /**
   * Options for the select component.
   * This can be an array of strings or objects with 'label' and 'value' properties.
   */
  options: (SelectOption | string)[];
  /**
   * Whether the select component has an error state.
   * If true, the select will be styled to indicate an error.
   */
  error?: boolean;
  /**
   * Whether the select component is clearable.
   * If true, a clear button will be displayed to reset the selection.
   */
  clearable?: boolean;
  /**
   * Whether the select component should be dense.
   * If true, the select will have a compact appearance.
   */
  dense?: boolean;
  /**
   * Whether the select component is disabled.
   * If true, the select will not respond to user interactions.
   */
  disable?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  clearable: true,
  dense: true,
  disable: false,
});

const emits = defineEmits<Emits>();

const normalizedOptions = computed(() =>
  props.options.map((option) =>
    typeof option === 'string' ? { label: option, value: option } : option,
  ),
);

const selectClasses = computed(() => [props.type, { 'error-select': props.error }]);

function handleUpdateValue(value: any): void {
  emits('update:modelValue', value);
}
</script>

<template>
  <div class="qselect-box" :class="selectClasses">
    <q-select
      :model-value="modelValue"
      @update:model-value="handleUpdateValue"
      :options="normalizedOptions"
      :label="label"
      :dense="dense"
      :disable="disable"
      :clearable="clearable"
      :lazy-rules="false"
      color="cyan-7"
      options-selected-class="text-deep"
      popup-content-class="popup"
      borderless
      behavior="menu"
      emit-value
      map-options
      :menu-offset="[0, -8]"
      v-bind="$attrs"
    >
      <template #option="{ itemProps, opt }">
        <q-item v-bind="itemProps" :class="`bg-opt-${type}`">
          <q-item-section>
            <q-item-label class="mt-2 text-base">
              {{ opt.label }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>
  </div>
</template>
