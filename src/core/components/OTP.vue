<script lang="ts" setup>
const KEYBOARD_KEYS = {
  BACKSPACE: 8,
  LEFT_ARROW: 37,
  RIGHT_ARROW: 39,
  DELETE: 46,
} as const;

const WIDTH_CLASSES: Record<number, string> = {
  6: '100%',
  5: '288px',
  4: '228px',
  3: '168px',
  2: '108px',
} as const;

interface Props {
  /**
   * Number of OTP input fields to display.
   * This determines how many individual inputs will be rendered.
   */
  numInputs: number;
  /**
   * Separator to be displayed between OTP input fields.
   * This can be a space, dash, or any other character.
   */
  separator?: string;
  /**
   * Classes to apply to the input fields.
   * This allows for custom styling of the input elements.
   */
  inputClasses?: string;
  /**
   * Type of input for the OTP fields.
   * This can be 'number', 'tel', 'password', or 'text'.
   */
  inputType: 'number' | 'tel' | 'password' | 'text';
  /**
   * Whether to automatically focus the first input field.
   * If true, the first input will be focused when the component is mounted.
   */
  autoFocus?: boolean;
  /**
   * Whether the OTP input is in an error state.
   * If true, the input fields will be styled to indicate an error.
   */
  error?: boolean;
  /**
   * Model value for the OTP input.
   * This is used to bind the current value of the OTP inputs.
   */
  modelValue?: string;
}

interface Emits {
  (event: 'onCompleted', value: string): void;
  (event: 'onChange', value: string): void;
}

interface SingleOTPChangeEvent {
  value: string;
  ref: { value: HTMLInputElement };
}

const props = withDefaults(defineProps<Props>(), {
  numInputs: 4,
  separator: ' ',
  inputType: 'tel',
  autoFocus: false,
});

const emits = defineEmits<Emits>();

const activeInputIndex = ref(0);
const otpValues = ref<string[]>([]);
const currentInputType = ref(props.inputType);
const inputRefs = ref<HTMLInputElement[]>([]);

let activeInputElement: HTMLInputElement | null = null;

const containerWidth = computed(() => {
  const baseWidth = WIDTH_CLASSES[props.numInputs];
  return props.inputType === 'password' ? `calc(${baseWidth} - 25px)` : baseWidth;
});

const isPasswordType = computed(() => props.inputType === 'password');
const otpString = computed(() => otpValues.value.join(''));
const isComplete = computed(() => otpString.value.length === props.numInputs);

function resetOTP(): void {
  otpValues.value = [];
}

watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) {
      resetOTP();
    }
  },
  { immediate: true },
);

function checkAndEmitCompletion(): void {
  if (isComplete.value) {
    activeInputElement?.blur();
    emits('onCompleted', otpString.value);
  }
}

async function setFocusToInput(index: number): Promise<void> {
  const targetIndex = Math.max(Math.min(props.numInputs - 1, index), 0);
  activeInputIndex.value = targetIndex;

  await nextTick();
  const targetInput = inputRefs.value[targetIndex];
  if (targetInput) {
    targetInput.focus();
  }
}

async function focusNextInput(): Promise<void> {
  if (activeInputIndex.value < props.numInputs - 1) {
    await setFocusToInput(activeInputIndex.value + 1);
  }
}

async function focusPreviousInput(): Promise<void> {
  if (activeInputIndex.value > 0) {
    await setFocusToInput(activeInputIndex.value - 1);
  }
}

function updateValueAtFocus(value: string): void {
  const previousOtpString = otpString.value;
  otpValues.value[activeInputIndex.value] = value;

  if (previousOtpString !== otpString.value) {
    emits('onChange', otpString.value);
    checkAndEmitCompletion();
  }
}

function isValidPastedData(data: string): boolean {
  if (currentInputType.value === 'number') {
    return /^\d+$/.test(data);
  }
  return true;
}

function handleFocus(index: number): void {
  activeInputIndex.value = index;
}

async function handleChange(event: SingleOTPChangeEvent): Promise<void> {
  const { value, ref } = event;
  activeInputElement = ref.value;
  updateValueAtFocus(value);

  // Only move to next input if a value was entered (not deleted)
  if (value) await focusNextInput();
}

async function handleKeyDown(event: KeyboardEvent): Promise<void> {
  const { keyCode } = event;

  switch (keyCode) {
    case KEYBOARD_KEYS.BACKSPACE:
      event.preventDefault();
      updateValueAtFocus('');
      await focusPreviousInput();
      break;

    case KEYBOARD_KEYS.DELETE:
      event.preventDefault();
      updateValueAtFocus('');
      break;

    case KEYBOARD_KEYS.LEFT_ARROW:
      event.preventDefault();
      await focusPreviousInput();
      break;

    case KEYBOARD_KEYS.RIGHT_ARROW:
      event.preventDefault();
      await focusNextInput();
      break;
  }
}

async function handlePaste(event: ClipboardEvent): Promise<void> {
  event.preventDefault();

  const pastedData = event.clipboardData?.getData('text/plain') || '';
  const relevantData = pastedData.slice(0, props.numInputs - activeInputIndex.value);

  if (!isValidPastedData(relevantData)) {
    return;
  }

  const pastedChars = relevantData.split('');
  const currentPrefix = otpValues.value.slice(0, activeInputIndex.value);
  const newOtpValues = currentPrefix.concat(pastedChars).slice(0, props.numInputs);

  otpValues.value = newOtpValues;
  await setFocusToInput(newOtpValues.length);

  emits('onChange', otpString.value);
  checkAndEmitCompletion();
}

function togglePasswordVisibility(show: boolean): void {
  currentInputType.value = show ? 'tel' : 'password';
}

onMounted(async () => {
  if (props.autoFocus) {
    await nextTick();
    await setFocusToInput(0);
  }
});
</script>

<template>
  <div
    class="v-otp-input relative"
    :style="{
      gridTemplateColumns: `repeat(${numInputs}, 1fr)`,
      width: containerWidth,
    }"
  >
    <SingleOTP
      v-for="(_, index) in numInputs"
      :key="index"
      :ref="
        (el: any) => {
          if (el?.$refs?.inputElement) {
            inputRefs[index] = el.$refs.inputElement;
          }
        }
      "
      :focus="activeInputIndex === index"
      :modelValue="otpValues[index] ?? ''"
      :separator="props.separator"
      :input-type="currentInputType"
      :input-classes="props.inputClasses || ''"
      :class="{
        'border-green': otpValues[index],
        'border-red': error,
      }"
      :is-last-child="index === numInputs - 1"
      :auto-focus="false"
      @on-change="handleChange"
      @on-keydown="handleKeyDown"
      @on-paste="handlePaste"
      @on-focus="handleFocus(index)"
    />

    <div
      v-if="isPasswordType"
      class="absolute -right-7 top-1/2 -translate-y-1/2 cursor-pointer"
      @pointerdown="togglePasswordVisibility(true)"
      @pointerup="togglePasswordVisibility(false)"
    >
      <Icon v-show="currentInputType === 'password'" name="icons/show" type="svg" />
      <Icon v-show="currentInputType === 'tel'" name="icons/hide" type="svg" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.v-otp-input {
  display: grid;
  gap: 8px;
}

.border-green {
  border: 1px solid #00f7ff !important;
}

.border-red {
  border: 1px solid #ff4545 !important;
}
</style>
