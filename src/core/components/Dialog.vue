<script setup lang="ts">
interface DialogProps {
  /**
   * Whether to hide the close button in the dialog.
   * If true, the close button will not be displayed.
   */
  hideClose?: boolean;
  /**
   * Whether to display the dialog in fullscreen mode.
   * If true, the dialog will cover the entire screen.
   */
  noHeader?: boolean;
  /**
   * Whether to display decorative crystals in the dialog.
   * If true, crystals will be shown in the dialog.
   */
  crystals?: boolean;
  /**
   * Whether to display a backdrop behind the dialog.
   * If true, a semi-transparent background will be shown.
   */
  backdrop?: boolean;
  /**
   * Whether to display a backdrop header in the dialog.
   * If true, a backdrop will be shown behind the header.
   */
  backdropHeader?: boolean;
  /**
   * Whether to display a bottom crystal in the dialog.
   * If true, a decorative crystal will be shown at the bottom.
   */
  bottomCrystal?: boolean;
  /**
   * Padding for the dialog content.
   * If specified, it will be applied as padding to the dialog content area.
   */
  px?: number;
  /**
   * Background color for the header of the dialog.
   * If specified, it will override the default background.
   */
  bgHeader?: string;
}

interface DialogEmits {
  (e: 'close'): void;
}

const props = defineProps<DialogProps>();
const emits = defineEmits<DialogEmits>();

const dialogPadding = computed(() =>
  props.px ? `16px ${props.px}px 28px ${props.px}px` : '16px 20px 28px 20px',
);

const headerBackgroundClass = computed(() => props.bgHeader || 'bg');

const showCloseButton = computed(() => !props.hideClose && !props.backdropHeader);
</script>

<template>
  <div class="dialog-fullscreen">
    <div v-if="backdrop" class="dialog-backdrop" />

    <div v-if="crystals" class="dialog-crystals">
      <HeaderCrystal />
    </div>

    <div class="dialog-wrapper">
      <div class="dialog-container">
        <slot name="icon-center" />
        <slot name="bottom-frame" />

        <Icon v-if="bottomCrystal" class="dialog-bottom-crystal" name="bottom-crystal-dialog" />

        <div class="dialog-btn-top-right">
          <slot name="btnTopRight" />
          <Button
            track-id="disable-track"
            v-if="showCloseButton"
            flat
            shape="square"
            size="small"
            @click="emits('close')"
          >
            <Icon name="cross" :size="16" />
          </Button>
        </div>

        <div class="dialog-btn-top-left">
          <slot name="btnTopLeft" />
        </div>

        <div v-if="!noHeader" class="dialog-header">
          <div v-if="backdropHeader" class="dialog-header-backdrop" />
          <div class="dialog-header-bg" :class="headerBackgroundClass" />
          <div class="dialog-header-content">
            <div class="dialog-header-text">
              <slot name="header" />
            </div>
          </div>
        </div>

        <div class="dialog-special-box">
          <slot name="special-box" />
        </div>

        <div class="dialog-sov">
          <slot name="sov" />
        </div>

        <div class="dialog-content">
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
$dialog-border-radius: 15px;
$dialog-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
$header-height: 60px;
$header-margin-top: 8vw;

.dialog-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  overflow: hidden;
}

.dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 8888;
}

.dialog-crystals {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
}

.dialog-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-container {
  position: relative;
  width: 100%;
  max-height: 85%;
  min-height: 200px;
  background-image:
    url('/imgs/dialog_top.png'), url('/imgs/dialog_center.png'), url('/imgs/dialog_bottom.png');
  background-position:
    center 1px,
    center 22.5px,
    center bottom;
  background-size:
    100% 22px,
    100% calc(100% - 62px),
    100% 40px;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  color: #ffffff;
}

// Action buttons
.dialog-btn-top-right,
.dialog-btn-top-left {
  position: absolute;
  top: -8px;
  z-index: 10;
}

.dialog-btn-top-right {
  right: -8px;
}

.dialog-btn-top-left {
  left: -8px;
}

// Header styles
.dialog-header {
  position: relative;
  width: 100%;
  height: $header-height;
  margin-top: $header-margin-top;
  text-align: center;
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;

  &-backdrop {
    position: absolute;
    top: -$header-margin-top;
    left: 0;
    width: 100%;
    height: calc(100% + #{$header-margin-top});
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
  }

  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
    z-index: 2;

    &.bg {
      background: linear-gradient(
        270deg,
        rgba(135, 100, 242, 0) -37.14%,
        #8764f2 46.61%,
        rgba(141, 118, 211, 0) 125.87%
      );
    }

    &.bg-island-bounty {
      background: linear-gradient(
        270deg,
        rgba(135, 100, 242, 0) -37.14%,
        rgba(245, 146, 0, 0.69) 46.61%,
        rgba(141, 118, 211, 0) 125.87%
      );
    }
  }

  &-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    padding: 20px;
  }

  &-text {
    font-size: 18px;
    font-weight: bold;
  }
}

// Content areas
.dialog-special-box {
  width: 100%;
}

.dialog-sov {
  position: absolute;
  top: 25%;
  left: 0;
}

.dialog-content {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: v-bind(dialogPadding);
  clip-path: polygon(
    100% 0,
    100% calc(100% - #{$dialog-border-radius}),
    0 calc(100% - #{$dialog-border-radius}),
    0 0
  );
}

.dialog-bottom-crystal {
  position: absolute;
  bottom: -8px;
  left: -15px;
  width: calc(100% + 30px);
  z-index: 10;
  pointer-events: none;
}
</style>
