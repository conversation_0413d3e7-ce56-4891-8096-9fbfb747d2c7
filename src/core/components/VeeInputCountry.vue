<script lang="ts" setup>
import { CountryRegion } from '@types';
import { useField } from 'vee-validate';

interface Emits {
  (event: 'update:country', country: CountryRegion): void;
}

interface Props {
  /**
   * The name of the input field, used for form submission and validation.
   */
  name: string;
  /**
   * Id for the input field.
   * If not provided, a default id will be generated.
   */
  id?: string;
  /**
   * Label for the input field, displayed above the input.
   */
  label?: string;
  /**
   * Default country to be selected in the input.
   * If not provided, no default country will be selected.
   */
  defaultCountry?: CountryRegion;
  /**
   * Custom list of countries to be used in the input.
   * If not provided, a default list of countries will be used.
   */
  customCountries?: CountryRegion[];
  /**
   * Whether the input is read-only.
   * If true, the input cannot be edited by the user.
   */
  readonly?: boolean;
  /**
   * Mask for the input field, useful for formatting input like phone numbers or dates.
   */
  mask?: string;
  /**
   * Whether the input should be focused automatically when the component is mounted.
   */
  autofocus?: boolean;
  /**
   * Whether to show an error state for the input.
   * This can be used to manually trigger an error state.
   */
  error?: boolean;
}

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const { errorMessage, value, meta } = useField(toRef(props, 'name'));

const inputId = computed(() => props.id ?? `vee-input-country-${props.name}`);
const hasValidationError = computed(() => Boolean(errorMessage.value && meta.touched));
const showError = computed(() => hasValidationError.value || props.error);
</script>

<template>
  <div class="vee-input-country-wrapper" v-bind="$attrs">
    <InputCountry
      class="vee-input-country mb-1"
      v-model="value"
      @update:country="(country: CountryRegion) => emits('update:country', country)"
      :readonly="props.readonly"
      :autofocus="props.autofocus"
      :error="showError"
      :id="inputId"
      v-bind="{
        ...(props.mask !== undefined && { mask: props.mask }),
        ...(props.defaultCountry !== undefined && { 'default-country': props.defaultCountry }),
        ...(props.customCountries !== undefined && { 'custom-countries': props.customCountries }),
        ...(props.label !== undefined && { label: props.label }),
      }"
    />
    <Transition name="error-fade">
      <div
        v-if="hasValidationError"
        class="vee-input-country__error text-center"
        v-html="errorMessage"
      />
    </Transition>
  </div>
</template>
<style lang="scss" scoped>
.vee-input-country-wrapper {
  position: relative;
}

:deep(.vee-input-country .q-field__bottom) {
  display: none;
}

.vee-input-country__error {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}

// Transition animations
.error-fade-enter-active,
.error-fade-leave-active {
  transition: all 0.2s ease;
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-0.5rem);
}
</style>
