<script setup lang="ts">
import { computed, toRef } from 'vue';
import { useField } from 'vee-validate';

interface VeeCheckboxProps {
  label: string;
  name: string;
  id?: string;
  error?: boolean;
}

const props = defineProps<VeeCheckboxProps>();

const { errorMessage, value, meta } = useField(toRef(props, 'name'));

const checkboxId = computed(() => props.id ?? `vee-checkbox-${props.name}`);
const hasError = computed(() => (!!errorMessage.value && !!meta.touched) || props.error);
const shouldShowError = computed(() => !!errorMessage.value && !!meta.touched);
</script>

<template>
  <div class="vee-checkbox-wrapper">
    <q-checkbox
      v-bind="$attrs"
      v-model="value"
      :label="undefined"
      :id="checkboxId"
      :error="hasError"
      class="vee-checkbox"
    >
      <div class="text-sm" v-html="label"></div>
    </q-checkbox>
    <Transition name="error-fade">
      <div v-if="shouldShowError" class="vee-checkbox__error" v-html="errorMessage" />
    </Transition>
  </div>
</template>

<style lang="scss" scoped>
.vee-checkbox-wrapper {
  position: relative;
}

:deep(.vee-checkbox .q-field__bottom) {
  display: none;
}

.vee-checkbox__error {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}
// Transition animations
.error-fade-enter-active,
.error-fade-leave-active {
  transition: all 0.2s ease;
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-0.5rem);
}
</style>
