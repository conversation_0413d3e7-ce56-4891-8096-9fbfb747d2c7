<script setup lang="ts">
const PASSWORD_PATTERNS = {
  DIGIT: /\d/,
  LOWERCASE: /[a-z]/,
  UPPERCASE: /[A-Z]/,
  SPECIAL: /[`~!@#$%^&*()\-_+=[{\]}\\|;:'",<.>/?]/,
} as const;

type ValidationKey = 'textLength' | 'lowercase' | 'uppercase' | 'digit' | 'special';
type ValidationState = Record<ValidationKey, boolean>;

interface Props {
  password: string;
}

interface Emits {
  (event: 'valid', isValid: boolean): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const { password } = toRefs(props);
const { t } = useI18n();

function usePasswordValidation(password: Ref<string>) {
  const validationState = computed<ValidationState>(() => ({
    textLength: password.value.length >= 8,
    lowercase: PASSWORD_PATTERNS.LOWERCASE.test(password.value),
    uppercase: PASSWORD_PATTERNS.UPPERCASE.test(password.value),
    digit: PASSWORD_PATTERNS.DIGIT.test(password.value),
    special: PASSWORD_PATTERNS.SPECIAL.test(password.value),
  }));

  const isAllValid = computed(() => Object.values(validationState.value).every(Boolean));

  return {
    validationState,
    isAllValid,
  };
}

const { validationState: valid, isAllValid } = usePasswordValidation(password);

watch(isAllValid, (newValue) => emits('valid', newValue), { immediate: true });

function getValidationClass(isValid: boolean): string {
  if (!password.value) return 'error';
  return isValid ? 'success' : 'error';
}
</script>

<template>
  <div class="requirements text-sm">
    <ul>
      <li :class="getValidationClass(valid.textLength)">
        <span class="circle"></span>
        <span v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_1')"></span>
      </li>
      <li :class="getValidationClass(valid.digit)">
        <span class="circle"></span>
        <span v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_2')"></span>
      </li>
      <li :class="getValidationClass(valid.lowercase)">
        <span class="circle"></span>
        <span v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_3')"></span>
      </li>
      <li :class="getValidationClass(valid.uppercase)">
        <span class="circle"></span>
        <span v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_4')"></span>
      </li>
      <li :class="getValidationClass(valid.special)">
        <span class="circle"></span>
        <span v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_5')"></span>
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.requirements {
  text-align: left;
  ul {
    padding-top: 12px;
    list-style: none;
    li {
      color: rgba(213, 213, 213, 0.9);
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      .circle {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 8px;

        display: inline-flex;
        justify-content: center;
        align-items: center;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
    .success {
      .circle {
        background-image: url('/imgs/correct.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
    .error {
      .circle {
        background-image: url('/imgs/uncorrect.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
  }
}
</style>
