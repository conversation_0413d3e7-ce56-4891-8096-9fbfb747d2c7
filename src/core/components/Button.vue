<script lang="ts" setup>
import { numeralFormat, playSFX } from '@utils';

interface Emits {
  (e: 'click'): void;
}

interface Props {
  /**
   * Unique identifier for tracking button clicks.
   * If tracking is not needed, this can be 'disable-track'.
   */
  trackId: 'disable-track' | (string & {});
  /**
   * Additional data to be sent with the tracking event.
   * This can include any relevant information for analytics.
   */
  trackData?: Record<string, unknown>;
  /**
   * Variant of the button, which determines its style.
   * Options include 'primary', 'secondary', 'purple', and 'neon'.
   */
  variant?: 'primary' | 'secondary' | 'purple' | 'neon';
  /**
   * Shape of the button, which affects its border radius.
   * Options include 'rounded', 'square', and 'medium-square'.
   */
  shape?: 'rounded' | 'square' | 'medium-square';
  /**
   * Size of the button, which affects its dimensions.
   * Options include 'medium', 'max-content', 'small', and 'medium-fit'.
   */
  size?: 'medium' | 'max-content' | 'small' | 'medium-fit';
  /**
   * Whether the button is disabled.
   * If true, the button will not respond to clicks.
   */
  disable?: boolean;
  /**
   * Whether to hide the sound effect when the button is clicked.
   * If true, no sound will play on click.
   */
  hideSFX?: boolean;
  /**
   * Icon to be displayed on the button.
   * This can be a string representing the icon name.
   */
  icon?: string;
  /**
   * Label to be displayed on the button.
   * This can be HTML content or plain text.
   */
  label?: string;
  /**
   * Whether the button is in a loading state.
   * If true, a loading spinner will be shown instead of the label.
   */
  loading?: boolean;
  /**
   * Whether the button should take the full width of its container.
   * If true, the button will expand to fill the available space.
   */
  block?: boolean;
  /**
   * Whether the button is a plus button.
   * If true, it will display a plus sign before the amount.
   */
  isPlus?: boolean;
  /**
   * Title to be displayed on the button.
   * This can be used to provide additional context or information.
   */
  title?: string;
  /**
   * Amount to be displayed on the button.
   * This can represent a cost, quantity, or any numerical value.
   */
  amount?: number;
  /**
   * Old amount to be displayed on the button.
   * This can be used to show a previous value, often with a strikethrough style.
   */
  oldAmount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  disable: false,
  size: 'medium',
  shape: 'rounded',
});
const emits = defineEmits<Emits>();

const { t } = useI18n();

const buttonClasses = computed(() => [
  'text-sm o-custom-btn',
  `o-${props.variant}-${props.shape}`,
  `o-${props.variant}`,
  `o-${props.shape}`,
  `o-${props.size}`,
  {
    '!w-full': props.block,
    loading: props.loading,
  },
]);

const customBtnClasses = computed(() => [
  'flex custom-btn flex-center',
  `${props.variant}-${props.shape}`,
  props.variant,
  props.shape,
  props.size,
  {
    'no-pointer-events': props.disable,
  },
]);

const hasAmountDisplay = computed(
  () => props.title && (props.amount !== undefined || props.amount === 0),
);

const showAmount = computed(() => props.amount && props.amount > 0);

const amountPrefix = computed(() => (props.isPlus ? '+' : '-'));

const handleClick = () => {
  if (!props.hideSFX) playSFX('button');
  emits('click');
};
</script>
<template>
  <q-btn
    class="o-btn-container"
    :class="buttonClasses"
    :label="undefined"
    :loading="false"
    :disable="disable"
    :push="!disable"
    :rounded="shape === 'rounded'"
    @click.stop="handleClick"
    v-tracker="{
      id: trackId,
      action: 'click',
      data: trackData,
    }"
    v-bind="$attrs"
  >
    <div :class="customBtnClasses">
      <q-circular-progress v-if="loading" indeterminate size="30" :thickness="0.3" />

      <template v-else>
        <div v-if="label && !hasAmountDisplay" v-html="label"></div>
        <slot v-if="!hasAmountDisplay"></slot>
        <div
          v-if="hasAmountDisplay"
          class="flex items-center justify-center flex-nowrap full-width full-height"
        >
          <div class="title" v-html="title"></div>
          <div v-if="showAmount" class="amount ml-[10px] whitespace-nowrap">
            {{ amountPrefix }}
            <Icon name="crystal" :size="20" class="mx-1" />
            <span v-if="oldAmount" class="line-through opacity-50 mx-0.5">
              {{ oldAmount }}
            </span>
            {{ numeralFormat(amount!) }}
          </div>
          <div v-else class="amount ml-[10px]">
            {{ t('BUTTON_FREE') }}
          </div>
        </div>
      </template>
    </div>
  </q-btn>
</template>

<style lang="scss" scoped>
.o-btn-container {
  height: 48px;
  width: max-content;
}
</style>
