<script lang="ts" setup>
import { useUserStore } from '@stores';
import gsap, { Linear } from 'gsap';
import { QCarousel } from 'quasar';

const SHORT_HOLD_THRESHOLD = 200; // ms
const FULL_PROGRESS = 100;

interface Illustration {
  name: string;
  type: 'gif' | 'png' | 'svg';
}

interface Props {
  /**
   * Duration for each slide in seconds.
   * Default is 8 seconds.
   */
  durations?: number;
  /**
   * Whether to display illustrations in multiple languages.
   * If true, the illustration name will be appended with the user's language.
   */
  multipleLang?: boolean;
  /**
   * List of illustrations to be displayed in the carousel.
   * Each illustration should have a name and type.
   */
  illustrations: Illustration[];
}

const props = withDefaults(defineProps<Props>(), {
  durations: 8,
});

const storeUser = useUserStore();

const { user } = storeToRefs(storeUser);

const currentSlide = ref<number>(1);
const slideProgress = ref<number[]>(props.illustrations.map(() => 0));
const autoplayDuration = ref<number>(props.durations);
const slideRef = ref<QCarousel>();
const holdStartTime = ref<number>(0);
const currentTween = ref<gsap.core.Tween | null>(null);

const totalSlides = computed(() => props.illustrations.length);
const hasMultipleSlides = computed(() => totalSlides.value > 1);
const currentSlideIndex = computed(() => currentSlide.value - 1);

const autoplayTime = computed(() =>
  autoplayDuration.value > 0 ? autoplayDuration.value * 1000 : false,
);

function cleanupTween(): void {
  if (currentTween.value) {
    currentTween.value.kill();
    currentTween.value = null;
  }
}

function resetProgressState(): void {
  autoplayDuration.value = props.durations;
  slideProgress.value = slideProgress.value.map((_, index) =>
    index < currentSlideIndex.value ? FULL_PROGRESS : 0,
  );
}

function calculateRemainingDuration(): number {
  const currentProgress = slideProgress.value[currentSlideIndex.value] ?? 0;
  return (props.durations / FULL_PROGRESS) * (FULL_PROGRESS - currentProgress);
}

function animateSlideProgress(slideIndex: number): void {
  cleanupTween();

  const progressIndex = slideIndex - 1;
  const currentProgress = slideProgress.value[progressIndex] ?? 0;
  const remainingProgress = FULL_PROGRESS - currentProgress;

  if (remainingProgress <= 0) return;

  const duration = (props.durations / FULL_PROGRESS) * remainingProgress;

  currentTween.value = gsap.to(slideProgress.value, {
    [progressIndex]: FULL_PROGRESS,
    duration,
    ease: Linear.easeNone,
  });
}

function handlePointerDown(): void {
  holdStartTime.value = performance.now();
  autoplayDuration.value = 0;
  cleanupTween();
}

function handlePointerUp(): void {
  const holdDuration = performance.now() - holdStartTime.value;
  const isShortHold = holdDuration <= SHORT_HOLD_THRESHOLD;

  if (isShortHold) {
    slideRef.value?.next();
  } else {
    autoplayDuration.value = calculateRemainingDuration();
    animateSlideProgress(currentSlide.value);
  }

  holdStartTime.value = 0;
}

watch(
  currentSlide,
  (newSlide) => {
    resetProgressState();
    animateSlideProgress(newSlide);
  },
  { immediate: false },
);

onMounted(async () => {
  await nextTick();
  animateSlideProgress(1);
});

onBeforeUnmount(() => {
  cleanupTween();
});
</script>
<template>
  <div class="relative w-full">
    <slot />
    <q-carousel
      animated
      v-model="currentSlide"
      :autoplay="autoplayTime"
      infinite
      ref="slideRef"
      class="rounded-xl w-full !h-auto"
    >
      <q-carousel-slide
        :name="index + 1"
        v-for="(item, index) in illustrations"
        :key="`slide_${index + 1}`"
        @pointerdown="handlePointerDown"
        @pointerup="handlePointerUp"
        class="!p-0 !w-full"
      >
        <Icon
          v-if="multipleLang"
          class="!w-full"
          :name="`${item.name}${user?.lang || 'en'}`"
          :type="item.type"
          lazy
        />
        <Icon v-else class="!w-full" :name="item.name" :type="item.type" lazy />
      </q-carousel-slide>
    </q-carousel>
    <div class="grid grid-flow-col grid-col-3 gap-1 mt-2" v-if="hasMultipleSlides">
      <div
        v-for="index in totalSlides"
        :key="`slide_progress_${index}`"
        class="h-1 rounded relative overflow-hidden bg-[#979797]"
      >
        <div
          class="w-full h-full bg-[#fd2b84] rounded transition-transform duration-75 ease-linear"
          :style="{
            transform: `translateX(-${FULL_PROGRESS - (slideProgress[index - 1] ?? 0)}%)`,
          }"
        ></div>
      </div>
    </div>
  </div>
</template>
