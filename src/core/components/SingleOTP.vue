<script lang="ts" setup>
import { HTMLAttributes } from 'vue';

const KEYBOARD_KEYS = {
  NUMERIC_START: 48,
  NUMERIC_END: 57,
  NUMPAD_START: 96,
  NUMPAD_END: 105,
  BACKSPACE: 8,
  DELETE: 46,
} as const;

interface Props {
  /**
   * Model value for the OTP input.
   * This is used to bind the current value of the OTP inputs.
   */
  modelValue?: string;
  /**
   * The prefix to be displayed before the OTP input.
   * This can be used for additional context or instructions.
   */
  separator?: string;
  /**
   * Classes to apply to the input fields.
   * This allows for custom styling of the input elements.
   */
  inputClasses?: string;
  /**
   * Whether to automatically focus the first input field.
   * If true, the first input will be focused when the component is mounted.
   */
  autoFocus?: boolean;
  /**
   * Type of input for the OTP fields.
   * This can be 'number', 'tel', 'password', or 'text'.
   */
  inputType?: 'number' | 'tel' | 'password' | 'text';
  /**
   * Mode for the input field.
   * This can be used to specify the input mode for better user experience on mobile devices.
   */
  inputmode?: HTMLAttributes['inputmode'];
}

interface Emits {
  (e: 'onChange', value: { value: string; ref: { value: HTMLInputElement } }): void;
  (e: 'onFocus'): void;
  (e: 'onBlur'): void;
  (e: 'onKeydown', event: KeyboardEvent): void;
  (e: 'onPaste', event: ClipboardEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  inputType: 'tel',
  autoFocus: false,
});
const emits = defineEmits<Emits>();

const inputElement = ref<HTMLInputElement | null>(null);
const inputValue = ref(props.modelValue || '');

const shouldShowSeparator = computed(() => props.separator);
const isEmpty = computed(() => !inputValue.value || inputValue.value.length === 0);
const isTextInput = computed(() => props.inputType === 'text');
const safeInputType = computed(() => props.inputType || 'text');

watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue || '';
  },
);

function isNumericKeyCode(keyCode: number): boolean {
  const isRegularNumeric =
    keyCode >= KEYBOARD_KEYS.NUMERIC_START && keyCode <= KEYBOARD_KEYS.NUMERIC_END;
  const isNumpadNumeric =
    keyCode >= KEYBOARD_KEYS.NUMPAD_START && keyCode <= KEYBOARD_KEYS.NUMPAD_END;

  if (isTextInput.value) {
    // For text inputs, allow both numeric and alphabetic characters
    return keyCode >= KEYBOARD_KEYS.NUMERIC_START && keyCode <= KEYBOARD_KEYS.NUMPAD_END;
  }

  return isRegularNumeric || isNumpadNumeric;
}

function isValidKeyPress(keyCode: number): boolean {
  const isNumeric = isNumericKeyCode(keyCode);
  const isControlKey = keyCode === KEYBOARD_KEYS.BACKSPACE || keyCode === KEYBOARD_KEYS.DELETE;

  return isNumeric || isControlKey;
}

function normalizeInputValue(value: string): string {
  return value.slice(0, 1);
}

function handleInput(event: Event): void {
  const target = event.target as HTMLInputElement;
  const normalizedValue = normalizeInputValue(target.value);

  inputValue.value = normalizedValue;
  target.value = normalizedValue; // Ensure the input element value is set

  if (inputElement.value) {
    emits('onChange', {
      value: normalizedValue,
      ref: { value: inputElement.value },
    });
  }
}

function handleKeyDown(event: KeyboardEvent): void {
  const keyCode = event.which || event.keyCode;

  if (isValidKeyPress(keyCode)) {
    emits('onKeydown', event);
  } else {
    event.preventDefault();
  }
}

function handlePaste(event: ClipboardEvent): void {
  emits('onPaste', event);
}

function handleFocus(): void {
  if (inputElement.value) {
    inputElement.value.select();
  }
  emits('onFocus');
}

function handleBlur(): void {
  emits('onBlur');
}

defineExpose({
  inputElement,
  focus: () => {
    if (inputElement.value) {
      inputElement.value.focus();
    }
  },
});

onMounted(async () => {
  if (props.autoFocus && inputElement.value) {
    await nextTick();
    inputElement.value.focus();
  }
});
</script>

<template>
  <div class="single-otp">
    <input
      ref="inputElement"
      :type="safeInputType"
      :value="inputValue"
      :class="[inputClasses, { 'border-white': isEmpty }]"
      :inputmode="inputmode || 'numeric'"
      min="0"
      max="9"
      maxlength="1"
      pattern="[0-9]"
      autocomplete="one-time-code"
      @input="handleInput"
      @keydown="handleKeyDown"
      @paste="handlePaste"
      @focus="handleFocus"
      @blur="handleBlur"
    />

    <span v-if="shouldShowSeparator" class="separator" v-html="separator" />
  </div>
</template>

<style lang="scss" scoped>
.single-otp {
  display: flex;
  align-items: center;
  gap: 4px;
}

.border-white {
  border-bottom: 1px solid #ffffff !important;
}

.separator {
  display: inline-block;
  user-select: none;
}
</style>
