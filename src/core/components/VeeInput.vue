<script setup lang="ts">
import { useField } from 'vee-validate';

type InputType =
  | 'number'
  | 'text'
  | 'search'
  | 'textarea'
  | 'time'
  | 'password'
  | 'email'
  | 'tel'
  | 'file'
  | 'url'
  | 'date';

interface VeeInputProps {
  /**
   * The name of the input field, used for form submission and validation.
   */
  name: string;
  /**
   * Label for the input field, displayed above the input.
   */
  label?: string;
  /**
   * Id for the input field.
   * If not provided, a default id will be generated.
   */
  id?: string;
  /**
   * Whether the input should be focused automatically when the component is mounted.
   */
  autofocus?: boolean;
  /**
   * Mask for the input field, useful for formatting input like phone numbers or dates.
   */
  mask?: string;
  /**
   * Maximum length of the input value.
   */
  maxlength?: number;
  /**
   * Whether to show an error state for the input.
   * This can be used to manually trigger an error state.
   */
  error?: boolean;
  /**
   * The type of the input field.
   * Defaults to 'text'.
   */
  type?: InputType;
  /**
   * Whether the input has a currency format.
   * If true, the input will be styled to indicate it is a currency input.
   */
  hasCurrency?: boolean;
}

const props = withDefaults(defineProps<VeeInputProps>(), {
  type: 'text',
  error: false,
  autofocus: false,
});

const { errorMessage, value, meta } = useField<string | number>(toRef(props, 'name'));

const inputId = computed(() => props.id ?? `vee-input-${props.name}`);
const hasValidationError = computed(() => Boolean(errorMessage.value && meta.touched));
const showError = computed(() => hasValidationError.value || props.error);
</script>

<template>
  <div class="vee-input-wrapper" v-bind="$attrs">
    <Input
      v-model="value"
      :id="inputId"
      :error="showError"
      class="vee-input mb-1"
      no-error-icon
      v-bind="{
        ...(label && { label }),
        ...(autofocus && { autofocus }),
        ...(mask && { mask }),
        ...(maxlength && { maxlength }),
        ...(type && { type }),
        ...(hasCurrency && { hasCurrency }),
      }"
    >
      <template #prepend>
        <slot name="prepend" />
      </template>
    </Input>

    <Transition name="error-fade">
      <div v-if="hasValidationError" class="vee-input__error text-center" v-html="errorMessage" />
    </Transition>
  </div>
</template>

<style lang="scss" scoped>
.vee-input-wrapper {
  position: relative;
}

:deep(.vee-input .q-field__bottom) {
  display: none;
}

.vee-input__error {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}

// Transition animations
.error-fade-enter-active,
.error-fade-leave-active {
  transition: all 0.2s ease;
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-0.5rem);
}
</style>
