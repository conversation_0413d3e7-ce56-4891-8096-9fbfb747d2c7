<script setup lang="ts">
import { useField } from 'vee-validate';

// Types
interface Props {
  /**
   * Field name for form validation binding
   */
  name: string;
  /**
   * Optional ID for the OTP component
   */
  id?: string;
  /**
   * Whether to automatically focus the first input
   */
  autoFocus?: boolean;
  /**
   * Number of OTP input fields
   */
  numInputs?: number;
  /**
   * Input type for OTP fields
   */
  inputType?: 'number' | 'tel' | 'password' | 'text';
  /**
   * Whether to show an error state
   */
  error?: boolean;
}

// Props with defaults
const props = withDefaults(defineProps<Props>(), {
  numInputs: 6,
  inputType: 'tel',
  autoFocus: false,
});

const { errorMessage, value, meta } = useField<string>(toRef(props, 'name'));

const componentId = computed(() => props.id ?? `vee-otp-${props.name}`);

const hasValidationError = computed(() => !!errorMessage.value && meta.touched);

const hasIncompleteError = computed(() => {
  if (!meta.touched) return false;
  const currentValue = String(value.value || '');
  return currentValue.length > 0 && currentValue.length < props.numInputs;
});

const hasError = computed(
  () => hasValidationError.value || hasIncompleteError.value || props.error,
);

function handleCompleted(completedValue: string): void {
  value.value = completedValue;
}

function handleChange(changedValue: string): void {
  value.value = changedValue;
}
</script>

<template>
  <div class="vee-otp-wrapper" v-bind="$attrs">
    <OTP
      v-model="value"
      :id="componentId"
      :input-type="props.inputType"
      :auto-focus="props.autoFocus"
      :num-inputs="props.numInputs"
      :error="hasError"
      @on-completed="handleCompleted"
      @on-change="handleChange"
      class="vee-otp mb-1"
    />

    <Transition name="error-fade">
      <div v-if="hasValidationError" class="vee-otp__error" v-html="errorMessage" />
    </Transition>
  </div>
</template>

<style lang="scss" scoped>
.vee-otp-wrapper {
  position: relative;
}

:deep(.vee-input .q-field__bottom) {
  display: none;
}

.vee-otp__error {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}

// Transition animations
.error-fade-enter-active,
.error-fade-leave-active {
  transition: all 0.2s ease;
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-0.5rem);
}
</style>
