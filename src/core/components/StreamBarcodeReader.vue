<script setup lang="ts">
import { BrowserMultiFormatReader, Exception, Result } from '@zxing/library';

interface Props {
  stream: MediaStream | null;
  width?: number;
  height?: number;
}

interface Emits {
  (event: 'decode', value: string): void;
  (event: 'loaded'): void;
  (event: 'error', error: Error): void;
}

const DEFAULT_SCANNER_SIZE = 297;

const emits = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  width: DEFAULT_SCANNER_SIZE,
  height: DEFAULT_SCANNER_SIZE,
});

const isLoading = ref(true);
const error = ref<Error | null>(null);
const scanner: Ref<HTMLVideoElement | null> = ref(null);
let codeReader: BrowserMultiFormatReader | null = null;

const isMediaStreamAPISupported = computed(
  () => navigator?.mediaDevices && 'enumerateDevices' in navigator.mediaDevices,
);

const scannerStyles = computed(() => ({
  width: `${props.width}px`,
  height: `${props.height}px`,
}));

function initializeCodeReader(): void {
  if (!codeReader) {
    codeReader = new BrowserMultiFormatReader();
  }
}

function handleDecodeResult(result: Result | null): void {
  if (result) {
    try {
      const decodedText = result.getText();
      emits('decode', decodedText);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to decode barcode');
      handleError(error);
    }
  }
}

function handleError(err: Error): void {
  console.error('StreamBarcodeReader error:', err);
  error.value = err;
  emits('error', err);
}

async function startScanning(): Promise<void> {
  if (!scanner.value || !codeReader) return;

  try {
    if (!props.stream) return;
    await codeReader.decodeFromStream(props.stream, scanner.value, handleDecodeResult);
  } catch (err) {
    const error = err instanceof Error ? err : new Error('Failed to start scanning');
    handleError(error);
  }
}

function setupVideoElement(): void {
  if (!scanner.value) return;

  scanner.value.oncanplay = () => {
    isLoading.value = false;
    emits('loaded');
  };

  scanner.value.onerror = () => {
    const error = new Error('Video element error');
    handleError(error);
  };
}

function cleanup(): void {
  if (codeReader) {
    codeReader.reset();
    codeReader = null;
  }
}

watch(
  () => props.stream,
  async (newStream) => {
    if (newStream) {
      isLoading.value = false;
      await startScanning();
    }
  },
);

onMounted(async () => {
  await nextTick();

  if (!isMediaStreamAPISupported.value) {
    throw new Exception('Media Stream API is not supported');
  }

  initializeCodeReader();
  setupVideoElement();
  await startScanning();
});

onBeforeUnmount(() => {
  cleanup();
});
</script>

<template>
  <div class="scanner-container flex flex-center">
    <div v-show="!isLoading" class="fit" style="border-radius: inherit">
      <video poster="data:image/gif,AAAA" ref="scanner" :style="scannerStyles" />
    </div>
    <div class="loading_qr flex flex-center" v-show="isLoading" :style="scannerStyles">
      <q-spinner color="primary" size="5em" :thickness="3" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading_qr {
  border-radius: 10px;
  background-color: #555555;
}

video {
  object-fit: cover;
  z-index: 999;
  display: inline-block;
  transform: unset;
  position: unset;
  top: unset;
  left: unset;
  border-radius: inherit;
}

.scanner-container {
  position: relative;
  text-align: center;
}
</style>
