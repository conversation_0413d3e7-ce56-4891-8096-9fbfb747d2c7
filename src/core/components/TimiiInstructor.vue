<script setup lang="ts">
import { useGlobalInstructor, usePageTracker } from '@composables';
import { UnifyInstructor, ActionCallback } from '@types';
import { BrandSponsors } from '@enums';
import gsap, { Power3, Linear } from 'gsap';
import TextPlugin from 'gsap/TextPlugin';
import CSSRulePlugin from 'gsap/CSSRulePlugin';

gsap.registerPlugin(TextPlugin, CSSRulePlugin);

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(defineProps<UnifyInstructor>(), {
  agent: '',
  hiddenAnims: false,
  bubbleAction: true,
});

const emits = defineEmits<Emits>();

const { closeUnifyInstructor: closeUnifyInstructorGlobal } = useGlobalInstructor();
const { tracker } = usePageTracker();

const tl = gsap.timeline();

const currentSequenceIndex = ref(0);
const isActionInProgress = ref(false);

const agentCharacter = computed(() => {
  return props.agent || `/sov/character/${BrandSponsors.DBS}_timii_profile`;
});

const currentSequence = computed(() => props.sequences[currentSequenceIndex.value]);
const hasMessage = computed(() => Boolean(currentSequence.value?.message));
const isLastSequence = computed(() => currentSequenceIndex.value >= props.sequences.length - 1);

const backdropStyle = computed(() => {
  const css = currentSequence.value?.backdropCss;
  return typeof css === 'object' ? css : undefined;
});

const instructorStyle = computed(() => {
  return currentSequence.value?.css || {};
});

// Validate teleport target exists
const teleportTarget = computed(() => {
  const target = currentSequence.value?.target || 'body';
  // Ensure target element exists in DOM
  if (typeof target === 'string') {
    const element = document.querySelector(target);
    return element ? target : 'body';
  }
  return 'body';
});

const SELECTORS = {
  TEXT: '#text',
  INSTRUCTOR: '.instructor',
  BACKDROP: '.i_backdrop',
  AGENT: '.agent',
  BODY: '.body',
  CROSS: '.cross',
  BUBBLE_NAME: '.bubble-name',
  ACTION: '.action',
} as const;

async function handleAction(action?: ActionCallback): Promise<void> {
  if (isActionInProgress.value || !currentSequence.value) return;

  isActionInProgress.value = true;

  try {
    if (currentSequence.value.persistent) return;

    if (!action) {
      await goToNext();
      return;
    }

    await action(() => void goToNext(), closeUnifyInstructor);
  } finally {
    isActionInProgress.value = false;
  }
}

async function goToNext(targetIndex?: number): Promise<void> {
  if (targetIndex !== undefined && targetIndex < props.sequences.length) {
    currentSequenceIndex.value = targetIndex;
    return;
  }

  if (!isLastSequence.value) {
    currentSequenceIndex.value += 1;
    return;
  }

  await closeUnifyInstructor();
}

async function closeUnifyInstructor(): Promise<void> {
  await hideWithTransition();
  closeUnifyInstructorGlobal();

  emits('close');
  tracker({
    id: 'timii_instructor',
    action: 'click',
    data: {
      target: 'tap',
      message: currentSequence.value?.message || '',
    },
  });

  isActionInProgress.value = false;
}

async function handleCloseWithAction(actionCallback?: ActionCallback): Promise<void> {
  if (actionCallback) {
    await actionCallback(() => void goToNext(), closeUnifyInstructor);
  } else {
    await closeUnifyInstructor();
  }
}

async function animateText(): Promise<void> {
  if (!hasMessage.value || !currentSequence.value?.message) return;

  const textElement = SELECTORS.TEXT;

  gsap.set(textElement, { pointerEvents: 'none' });

  try {
    await gsap.fromTo(
      textElement,
      { text: '' },
      {
        delay: currentSequenceIndex.value ? 0 : 1,
        text: currentSequence.value.message,
        duration: 1,
        ease: Linear.easeNone,
      },
    );
  } finally {
    gsap.set(textElement, { pointerEvents: 'all' });
  }
}

watchEffect(() => void animateText(), { flush: 'post' });

async function showWithTransition(): Promise<void> {
  const instructorElement = SELECTORS.INSTRUCTOR;

  gsap.set(instructorElement, { pointerEvents: 'none' });

  try {
    if (!hasMessage.value) return;

    await tl
      .fromTo(
        SELECTORS.AGENT,
        { y: 12, xPercent: 1, opacity: 0 },
        { y: 0, xPercent: 2, opacity: 1, duration: 0.8 },
      )
      .fromTo(SELECTORS.BODY, { opacity: 0, width: 0 }, { opacity: 1, width: '100%', duration: 1 })
      .fromTo(SELECTORS.CROSS, { opacity: 0, x: -5 }, { opacity: 1, x: 0 });
  } finally {
    gsap.set(instructorElement, { pointerEvents: 'all' });
  }
}

async function hideWithTransition(): Promise<void> {
  const elements = [SELECTORS.INSTRUCTOR, SELECTORS.BACKDROP];

  gsap.set(elements, { pointerEvents: 'none' });

  try {
    if (!hasMessage.value) return;

    await tl
      .to(SELECTORS.BODY, {
        y: -5,
        opacity: 0,
        duration: 0.5,
        ease: Power3.easeIn,
      })
      .to([SELECTORS.AGENT, SELECTORS.BUBBLE_NAME], { opacity: 0, y: 10 }, '-=0.5');
  } finally {
    gsap.set(elements, { pointerEvents: 'all' });
  }
}

onMounted(async () => {
  await nextTick();
  // Ensure DOM is ready before starting transitions
  if (document.body && hasMessage.value) {
    await showWithTransition();
  }
});

onBeforeUnmount(() => {
  // Kill all animations immediately to prevent DOM manipulation errors
  tl.kill();

  const elementsToCleanup = [
    SELECTORS.ACTION,
    SELECTORS.TEXT,
    SELECTORS.INSTRUCTOR,
    SELECTORS.BACKDROP,
    SELECTORS.AGENT,
    SELECTORS.BODY,
    SELECTORS.BUBBLE_NAME,
  ];

  elementsToCleanup.forEach((selector) => {
    gsap.killTweensOf(selector);
  });
});
</script>

<template>
  <Teleport :to="teleportTarget">
    <!-- Animated backdrop -->
    <Transition
      appear
      enter-active-class="animated fadeIn"
      leave-active-class="animated fadeOut"
      :duration="300"
    >
      <div
        v-if="currentSequence?.backdropCss"
        class="i_backdrop"
        v-bind="backdropStyle ? { style: backdropStyle } : {}"
        @click="handleAction(currentSequence.actions?.cb)"
      />
    </Transition>

    <!-- Main instructor container -->
    <div class="instructor" :style="instructorStyle">
      <div v-if="hasMessage" class="wrapper">
        <!-- Agent avatar -->
        <div class="agent">
          <Icon :name="agentCharacter" class="w-full h-full object-cover" />
        </div>

        <!-- Message body -->
        <div class="body">
          <!-- Message text -->
          <div
            id="text"
            v-html="currentSequence?.message"
            @click="handleAction(currentSequence?.actions?.cb)"
          />

          <!-- Close button -->
          <div
            v-if="!currentSequence?.hideClose"
            class="cross absolute top-1 right-1 w-6 h-6 flex justify-center items-center"
            @click="handleCloseWithAction(currentSequence?.actions?.closeX)"
          >
            <Icon :size="14" name="cross" />
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style lang="scss" scoped>
.i_backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 7000;
}
.instructor {
  z-index: 10001;
  position: fixed;
  left: 40px;
  bottom: 40px;
  right: 80px;
  transition: bottom 0.4s ease-in-out;

  .wrapper {
    position: relative;
    display: flex;
    width: 100%;
  }
  .agent {
    position: absolute;
    top: -5px;
    left: -25px;
    z-index: 3;
    width: 57px;
    height: 57px;
    background-image: url('/imgs/profile.png');
    background-size: 100% 100%;
    padding: 5px;
  }
  .body {
    position: relative;
    display: flex;
    align-items: center;
    height: max-content;
    min-height: 100px;
    padding: 10px 15px 10px 40px;
    background-size: 100% 100%;
    background-image: url('/imgs/intro-speech.png');
    background-repeat: no-repeat;
    #text {
      width: 100vw;
      padding: 10px 0 5px;
    }
  }
}
</style>
