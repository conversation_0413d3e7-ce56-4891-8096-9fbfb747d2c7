<script lang="ts" setup>
import axios from 'axios';
import Plyr from 'plyr';
import 'plyr/dist/plyr.css';

const YOUTUBE_REGEX = /(youtu\.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=|shorts\/)([^#&?]*).*/;
const DEFAULT_VIDEO_ID = '6gb5bmb545E';
const YOUTUBE_API_BASE = 'https://www.googleapis.com/youtube/v3/videos';

interface YouTubeThumbnails {
  maxres?: { url: string };
  high?: { url: string };
  standard?: { url: string };
}

interface YouTubeApiResponse {
  items: Array<{
    snippet: {
      thumbnails: YouTubeThumbnails;
    };
  }>;
}

interface Emits {
  (event: 'play'): void;
  (event: 'pause'): void;
  (event: 'ended'): void;
  (event: 'ready'): void;
}

interface Props {
  /**
   * The YouTube video URL or ID to be played.
   * If the URL is invalid or the ID cannot be extracted, a default video will be used.
   */
  source: string;
  /**
   * Whether the video should start playing automatically when loaded.
   * Default is false, meaning the video will not autoplay.
   */
  autoPlay?: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

let player: Plyr | null = null;
const apiKey = process.env.APP_GOOGLE_API_KEY;
const playerRef = ref<HTMLDivElement | null>(null);
const isPause = ref(false);
const isEnd = ref(false);
const loaded = ref(false);

const sourceId = computed(() => extractYouTubeId(props.source));

function extractYouTubeId(url: string): string {
  const match = url.match(YOUTUBE_REGEX);
  return match && match[2]?.length === 11 ? match[2] : DEFAULT_VIDEO_ID;
}

function getBestThumbnail(thumbnails: YouTubeThumbnails): string {
  return thumbnails?.maxres?.url ?? thumbnails?.high?.url ?? thumbnails?.standard?.url ?? '';
}

function setupPlayerEvents(player: Plyr): void {
  player.on('ready', () => {
    emits('ready');
    loaded.value = true;
  });

  player.on('play', () => emits('play'));
  player.on('pause', () => emits('pause'));
  player.on('ended', () => {
    isEnd.value = true;
    emits('ended');
  });
}

function createPlyrConfig(thumbnail = ''): Plyr.Options {
  return {
    controls: ['play'],
    clickToPlay: true,
    autoplay: props.autoPlay ?? false,
    youtube: {
      noCookie: false,
      showinfo: 0,
      iv_load_policy: 3,
      modestbranding: 1,
      fs: 0,
      rel: 0,
    },
    previewThumbnails: {
      enabled: !!thumbnail,
      src: thumbnail,
    },
  };
}

async function fetchThumbnail(videoId: string): Promise<string> {
  const { data }: { data: YouTubeApiResponse } = await axios.get(
    `${YOUTUBE_API_BASE}?id=${videoId}&key=${apiKey}&part=snippet`,
    { timeout: 5000 },
  );

  const thumbnailData = data.items[0]?.snippet?.thumbnails;
  return getBestThumbnail(thumbnailData ?? {});
}

function createPlayer(thumbnail = ''): void {
  if (!playerRef.value) {
    throw new Error('Player reference not available');
  }

  const config = createPlyrConfig(thumbnail);
  player = new Plyr(playerRef.value, config);
  setupPlayerEvents(player);
}

async function getVideo(): Promise<void> {
  if (!playerRef.value || !sourceId.value) return;

  const thumbnail = apiKey ? await fetchThumbnail(sourceId.value) : '';
  createPlayer(thumbnail);
}

async function play(): Promise<void> {
  if (!player) return;
  await player.play();
}

function pause(): void {
  if (!player) return;
  player.pause();
}

watch(isPause, async (val) => {
  if (!player) return;

  if (val) pause();
  else await play();
});

onMounted(async () => {
  await nextTick();
  await getVideo();
});

onBeforeUnmount(() => {
  if (player) {
    player.destroy();
    player = null;
  }
});

defineExpose({
  play,
  pause,
});
</script>
<template>
  <div class="relative flex items-center justify-center w-full h-full">
    <slot v-if="loaded" />
    <q-circular-progress
      v-if="!loaded"
      class="absolute z-30 -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
      indeterminate
      rounded
      size="50px"
      color="white"
    />
    <div
      :class="{
        'pointer-events-none': !loaded,
      }"
      ref="playerRef"
      data-plyr-provider="youtube"
      :data-plyr-embed-id="sourceId"
    ></div>
  </div>
</template>
<style lang="scss">
.plyr--video {
  width: 100% !important;
}
iframe {
  .ytp-chrome-top {
    display: none;
  }
}
.plyr__poster {
  background-size: cover !important;
}
</style>
