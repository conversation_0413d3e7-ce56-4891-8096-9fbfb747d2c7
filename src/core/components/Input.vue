<script lang="ts" setup>
type InputType =
  | 'number'
  | 'text'
  | 'search'
  | 'textarea'
  | 'time'
  | 'password'
  | 'email'
  | 'tel'
  | 'file'
  | 'url'
  | 'date';

interface Props {
  /**
   * Label for the input field.
   * This will be displayed above the input.
   */
  label?: string;
  /**
   * Whether the input has an error state.
   * If true, the input will be styled to indicate an error.
   */
  error?: boolean;
  /**
   * Custom prefix width in pixels.
   * This will adjust the padding of the input to accommodate a custom prefix.
   */
  customPrefix?: number;
  /**
   * Type of the input field.
   * This determines the behavior and appearance of the input.
   * Default is 'text'.
   */
  type?: InputType;
  /**
   * Model value for the input.
   * This is the value bound to the input field.
   */
  modelValue?: string | number;
  /**
   * Whether the input is read-only.
   * If true, the input cannot be edited by the user.
   */
  readonly?: boolean;
  /**
   * Prefix to be displayed in the input.
   * This can be a string or a number that appears before the input value.
   */
  prefix?: string;
  /**
   * Placeholder text for the input.
   * This will be displayed when the input is empty.
   */
  placeholder?: string;
  /**
   * Whether the input should have a mobile-friendly legend.
   * If true, the input will be styled for better usability on mobile devices.
   */
  hasMobileLegend?: boolean;
  /**
   * Whether the input has a select dropdown.
   * If true, the input will be styled to indicate it can open a select menu.
   */
  hasSelect?: boolean;
  /**
   * Whether the input should be centered.
   * If true, the input will be aligned to the center of its container.
   */
  center?: boolean;
  /**
   * Whether the input should be autofocus.
   * If true, the input will automatically focus when the component is mounted.
   */
  autofocus?: boolean;
  /**
   * Mask for the input.
   * This can be used to format the input value, such as for phone numbers or dates.
   */
  mask?: string;
  /**
   * Maximum length of the input value.
   * This limits the number of characters that can be entered into the input.
   */
  maxlength?: number;
  /**
   * Whether the input has a currency format.
   * If true, the input will be styled to indicate it is a currency input.
   */
  hasCurrency?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string | number | null): void;
  (e: 'focus', event: FocusEvent): void;
  (e: 'blur', event: FocusEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  label: '',
  error: false,
  type: 'text',
  readonly: false,
  customPrefix: 0,
});

const emits = defineEmits<Emits>();

const inputType = ref<InputType>(props.type);
const focused = ref(false);

const isInputFocused = computed(
  () =>
    focused.value ||
    !!props.modelValue ||
    !!props.prefix ||
    props.hasMobileLegend ||
    props.hasSelect,
);

const labelClasses = computed(() => [
  props.error ? 'text-error' : 'text-normal',
  props.center && 'center',
]);

const inputClasses = computed(() => [props.error && 'error-input']);

const inputStyles = computed(() => ({
  color: props.error ? '#ff0000' : '#202020',
  ...(props.customPrefix &&
    !props.modelValue && {
      paddingLeft: `${props.customPrefix}px`,
    }),
}));

const labelStyles = computed(() =>
  props.customPrefix && !props.modelValue ? { paddingLeft: `${props.customPrefix}px` } : {},
);

const containerClasses = computed(() => [
  'legend-input',
  isInputFocused.value && 'input-focused',
  props.hasMobileLegend && 'has-mobile-number',
  props.hasSelect && 'has-select',
]);

const showPasswordIcon = computed(() => props.type === 'password');
const showSearchIcon = computed(() => props.type === 'search');

const togglePasswordVisibility = (): void => {
  inputType.value = inputType.value === 'password' ? 'text' : 'password';
};

const handleInput = (value: string | number | null): void => {
  emits('update:modelValue', value);
};

const handleFocus = (event: FocusEvent): void => {
  focused.value = true;
  emits('focus', event);
};

const handleBlur = (event: FocusEvent): void => {
  focused.value = false;
  emits('blur', event);
};

onMounted(async () => {
  if (!props.autofocus) return;

  await nextTick();
  const inputEl = document.querySelector('[data-focus]') as HTMLInputElement;
  inputEl?.focus({ preventScroll: true });
});
</script>

<template>
  <div class="legend-input" :class="containerClasses">
    <div class="label" :class="labelClasses" :style="labelStyles">
      {{ label }}
    </div>

    <q-input
      :placeholder="placeholder"
      :class="inputClasses"
      :model-value="modelValue"
      @update:model-value="handleInput"
      @focusin="handleFocus"
      @focusout="handleBlur"
      outlined
      dense
      :type="inputType"
      :input-style="inputStyles"
      :input-class="[hasCurrency && '!text-2xl font-bold text-right']"
      :readonly="readonly"
      :prefix="prefix"
      :autofocus="autofocus"
      :mask="mask"
      :maxlength="maxlength"
      data-focus
      v-bind="$attrs"
    >
      <slot />

      <template #prepend>
        <slot name="prepend" />
      </template>

      <template #append>
        <div
          v-if="showPasswordIcon"
          class="icon-password"
          role="button"
          tabindex="0"
          @click="togglePasswordVisibility"
        >
          <Icon v-show="inputType === 'password'" name="icons/show" type="svg" />
          <Icon v-show="inputType === 'text'" name="icons/hide" type="svg" />
        </div>

        <div v-if="showSearchIcon" class="icon-password">
          <Icon name="search" type="svg" />
        </div>
      </template>
    </q-input>
  </div>
</template>
