<script lang="ts" setup>
import { countries } from '@utils';
import { CountryRegion } from '@types';
import { useUserStore } from '@stores';

interface Emits {
  (event: 'update:country', country: CountryRegion): void;
}

interface Props {
  /**
   * The currently selected country.
   * This will be updated when the user selects a different country.
   */
  label?: string;
  /**
   * The default country to be selected when the component is mounted.
   * If not provided, it will default to the first country in the list.
   */
  defaultCountry?: CountryRegion;
  /**
   * Custom list of countries to be displayed in the dropdown.
   * If not provided, it will use the default list of countries.
   */
  customCountries?: CountryRegion[];
  /**
   * Whether the input is read-only.
   * If true, the user cannot change the selected country.
   */
  readonly?: boolean;
  /**
   * Mask for the input.
   * This can be used to format the input value, such as for phone numbers or dates.
   */
  mask?: string;
  /**
   * Whether the input should be autofocus.
   * If true, the input will automatically focus when the component is mounted.
   */
  autofocus?: boolean;
  /**
   * Whether the input should display an error state.
   * If true, the input will be styled to indicate an error.
   */
  error?: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { seasonCode } = storeToRefs(storeUser);

const availableCountries = computed<CountryRegion[]>(() => props.customCountries ?? countries);

const country = ref<CountryRegion | null>(null);

const selectedCountry = computed<CountryRegion>(
  () => country.value ?? availableCountries.value[0] ?? countries[0]!,
);

function findCountryByIso(iso: string): CountryRegion | undefined {
  return availableCountries.value.find((c) => c.iso === iso);
}

function getInitialCountry(): CountryRegion {
  if (props.defaultCountry) {
    return props.defaultCountry;
  }

  if (seasonCode.value) {
    const seasonCountry = findCountryByIso(seasonCode.value);
    if (seasonCountry) {
      return seasonCountry;
    }
  }

  return availableCountries.value[0] ?? countries[0]!;
}

function updateCountry(newCountry: CountryRegion): void {
  country.value = newCountry;
  emits('update:country', newCountry);
}

watch(
  () => props.defaultCountry,
  (newDefault) => {
    if (newDefault) updateCountry(newDefault);
  },
  { immediate: false },
);

watch(
  () => props.customCountries,
  (newCustomCountries) => {
    if (newCustomCountries && country.value) {
      const isValidCountry = newCustomCountries.some((c) => c.iso === country.value?.iso);
      if (!isValidCountry) {
        updateCountry(newCustomCountries[0]!);
      }
    }
  },
);

function handleCountryChange(selectedCountry: CountryRegion): void {
  updateCountry(selectedCountry);
}

onMounted(() => {
  const initialCountry = getInitialCountry();
  updateCountry(initialCountry);
});
</script>

<template>
  <Input
    v-bind="{
      ...$attrs,
      ...(label !== undefined && { label }),
      ...(readonly !== undefined && { readonly }),
      ...(mask !== undefined && { mask: props.mask }),
      ...{ autofocus: props.autofocus },
      ...{ error: props.error },
      hasSelect: true,
    }"
  >
    <template #prepend>
      <div class="relative flex items-center ml-[10px]">
        <div class="absolute w-[1px] h-4 bg-white opacity-50 -right-2 top-4"></div>
        <div
          class="text-sm text-white mt-[6px] pointer-events-none"
          v-html="selectedCountry.code"
        ></div>
        <div class="qselect-country">
          <q-select
            class="relative"
            v-model="country"
            :options="availableCountries"
            options-selected-class="selected"
            borderless
            behavior="menu"
            dense
            popup-content-class="popup"
            :readonly="readonly"
            @update:model-value="handleCountryChange"
          >
            <template #option="scope">
              <div class="fit">
                <q-item v-bind="scope.itemProps">
                  <q-item-section avatar>
                    <q-img :src="scope.opt.flag" class="w-8 h-5" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="mt-2">
                      {{ scope.opt.name }}
                    </q-item-label>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="mt-2"> ({{ scope.opt.code }}) </q-item-label>
                  </q-item-section>
                </q-item>
              </div>
            </template>
          </q-select>
        </div>
      </div>
    </template>
  </Input>
</template>
<style lang="scss">
.qselect-country {
  margin-top: 10px;

  .q-field__control {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    width: 20px;
    height: 30px !important;
    min-height: 30px !important;
  }

  .q-field__native {
    display: none !important;
  }
  .selected-country .normal-flag {
    margin: 0 !important;
  }
  .q-field__append {
    margin-top: -4px;
  }
  .q-field__append .q-icon {
    position: absolute;
    width: 100px;
    top: 0;
    right: -40px;
    color: #00f7ff;
  }
  .flag {
    top: 7px;
    left: 7px;
    position: absolute;
  }
}
</style>
