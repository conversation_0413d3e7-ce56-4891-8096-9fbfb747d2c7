<script setup lang="ts">
import { useField } from 'vee-validate';

interface SelectOption {
  label: string;
  value: any;
  disable?: boolean;
}

interface Props {
  /**
   * The name of the select field, used for form submission and validation.
   */
  name: string;
  /**
   * Label for the select field, displayed above the select.
   */
  label?: string;
  /**
   * Options for the select field, can be an array of SelectOption objects or strings.
   * Each option should have a 'label' and 'value' property.
   */
  options: (SelectOption | string)[];
  /**
   * Id for the select field.
   * If not provided, a default id will be generated.
   */
  id?: string;
  /**
   * Whether the select should be focused automatically when the component is mounted.
   */
  error?: boolean;
}

const props = defineProps<Props>();

const { errorMessage, value, meta } = useField(toRef(props, 'name'));

const fieldId = computed(() => props.id ?? `vee-select-${props.name}`);

const hasError = computed(() => (!!errorMessage.value && meta.touched) || props.error);

const showErrorMessage = computed(() => !!errorMessage.value && meta.touched);
</script>

<template>
  <div class="vee-select-wrapper" v-bind="$attrs">
    <Select
      v-model="value"
      :label="label || ''"
      :options="options"
      :id="fieldId"
      :error="hasError"
      class="vee-select mb-1"
      no-error-icon
    />

    <Transition name="error-fade">
      <div v-if="showErrorMessage" class="vee-select__error text-center" v-html="errorMessage" />
    </Transition>
  </div>
</template>

<style lang="scss" scoped>
.vee-select-wrapper {
  position: relative;
}

:deep(.vee-select .q-field__bottom) {
  display: none;
}

.vee-select__error {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}

// Transition animations
.error-fade-enter-active,
.error-fade-leave-active {
  transition: all 0.2s ease;
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-0.5rem);
}
</style>
