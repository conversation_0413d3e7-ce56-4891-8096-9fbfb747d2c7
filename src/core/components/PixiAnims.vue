<script lang="ts" setup>
import { Application } from 'pixi.js';
import { usePixiAnimations } from '@composables';

interface Props {
  /**
   * The name of the animation to be displayed.
   * This is used to identify the animation in the Pixi.js application.
   */
  name: string;
  /**
   * The JSON data for the animation.
   * This should be a string containing the JSON representation of the animation.
   */
  json: string;
  /**
   * The speed of the animation.
   * This is a multiplier for the default animation speed.
   * Default is 0.5, which means the animation will play at half speed.
   */
  animationSpeed?: number;
  /**
   * The size of the animation.
   * This can be 'cover' or 'contain', which determines how the animation fits within its container.
   * Default is 'cover'.
   */
  size?: 'cover' | 'contain';
  /**
   * The width of the animation in pixels.
   * If not provided, it will default to the width of the canvas element.
   */
  width?: number;
  /**
   * The height of the animation in pixels.
   * If not provided, it will default to the height of the canvas element.
   */
  height?: number;
}

interface PixiInstance {
  app: Application;
  clearCache: () => void;
  destroy: () => void;
}

const props = defineProps<Props>();

// Reactive state
const pixiCanvas = ref<HTMLCanvasElement | null>(null);
const pixiInstance = ref<PixiInstance | null>(null);
const isLoading = ref(false);

// Computed
const shouldShowCanvas = computed(() => pixiCanvas.value);

async function initializePixi(): Promise<void> {
  if (!pixiCanvas.value) {
    throw new Error('Canvas element not found');
  }
  isLoading.value = true;

  try {
    const { app, destroy, clearCache } = await usePixiAnimations({
      id: pixiCanvas.value,
      name: props.name,
      json: props.json,
      animationSpeed: props.animationSpeed || 0.5,
      width: props.width || pixiCanvas.value.clientWidth,
      height: props.height || pixiCanvas.value.clientHeight,
      size: props.size || 'cover',
    });
    pixiInstance.value = { app, destroy, clearCache };
  } catch (err) {
    console.error('PIXI Animation Error:', err);
  } finally {
    isLoading.value = false;
  }
}

function cleanup(): void {
  if (pixiInstance.value) {
    pixiInstance.value.destroy();
    pixiInstance.value.clearCache();
    pixiInstance.value = null;
  }
}

onMounted(async () => {
  await nextTick();
  await initializePixi();
});

onBeforeUnmount(() => {
  cleanup();
});
</script>

<template>
  <canvas v-show="shouldShowCanvas && !isLoading" ref="pixiCanvas" />
</template>
