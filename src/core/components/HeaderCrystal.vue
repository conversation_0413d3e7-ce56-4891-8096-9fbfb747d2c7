<script setup lang="ts">
import { numeralFormat } from '@utils';
import { useUserStore } from '@stores';

const store = useUserStore();

const { crystals } = storeToRefs(store);
</script>
<template>
  <div
    style="background: linear-gradient(180deg, rgba(35, 0, 69, 0.8) 0%, #5b1b9b 100%)"
    class="px-2 py-2 rounded-md flex items-center justify-center gap-1 w-[82px]"
  >
    <Icon name="crystal-s" :size="16" />
    <div
      class="text-xs font-bold"
      v-html="crystals > 99999 ? `99,999<sup>+</sup>` : numeralFormat(crystals)"
    ></div>
  </div>
</template>
