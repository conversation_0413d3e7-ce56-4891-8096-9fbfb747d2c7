import { AsyncComponentLoader } from 'vue';

export enum MICRO_ROUTER {
  STORE = 'GLOBAL_MICRO_ROUTER_STORE',
}

export type TransitionType =
  | 'slide-right'
  | 'slide-left'
  | 'slide-up'
  | 'slide-down'
  | 'fade'
  | 'scale'
  | 'rotate'
  | 'flip-right'
  | 'flip-left'
  | 'flip-up'
  | 'flip-down'
  | 'jump-right'
  | 'jump-left'
  | 'jump-up'
  | 'jump-down';

export type DialogPosition = 'standard' | 'top' | 'right' | 'bottom' | 'left';

export interface MicroRoute {
  path: string;
  component: AsyncComponentLoader | Component;
  attrs?: Record<string, unknown> | undefined;
  bgm?: string;
  slotVisible?: boolean;
}

export interface MicroDialog {
  path: string;
  component: AsyncComponentLoader | Component;
  actived: boolean;
  fullscreen?: boolean;
  persistent?: boolean;
  attrs?: Record<string, unknown> | undefined;
  position?: DialogPosition;
  transitionDuration?: number;
  transitionShow?: TransitionType;
  transitionHide?: TransitionType;
}

export interface MicroControl {
  name: string;
  activated: boolean;
  component: AsyncComponentLoader | Component;
  attrs?: Record<string, unknown> | undefined;
}

export interface MicroRouterOptions {
  defaultPath: string;
  routesGetter?: MicroRoute[];
  dialogGetter?: MicroDialog[];
  key?: string;
}

export interface DialogProps {
  path: string;
  open?: boolean;
  attrs?: Record<string, unknown> | undefined;
}

export interface DialogInstance {
  path: string;
  attrs?: Record<string, unknown> | undefined;
}

export interface MicroRouterStore {
  _from: ComputedRef<string>;
  _to: ComputedRef<string>;
  fromPage: ComputedRef<string>;
  toPage: ComputedRef<string>;
  activePage: ComputedRef<string>;
  activeDialog: ComputedRef<string>;
  fromDialog: ComputedRef<string>;
  toDialog: ComputedRef<string>;
  resolveRoutes: ComputedRef<MicroRoute[]>;
  resolveDialogs: ComputedRef<MicroDialog[]>;
  resolveControls: ComputedRef<MicroControl[]>;
  push: (path: string | number, props?: Record<string, unknown>) => void;
  openDialog: (path: string, props?: Record<string, unknown>) => DialogInstance;
  closeDialog: (path: string) => void;
  toggleControl: (path: string, show: boolean) => void;
  closeAllDialogs: () => void;
  registerRoute: (route: MicroRoute) => void;
  registerDialog: (dialog: MicroDialog) => void;
  registerRoutes: (routes: MicroRoute[]) => void;
  registerControls: (routes: MicroControl[]) => void;
  registerDialogs: (dialogs: MicroDialog[]) => void;
  playSound: (soundSrc: string, loop: boolean) => void;
  stopSound: () => void;
  pauseSound: () => void;
  fadeSound: (from: number, to: number, duration: number) => Promise<void>;
}
