import { Howl } from 'howler';
import {
  DialogInstance,
  DialogProps,
  MICRO_ROUTER,
  MicroDialog,
  MicroRoute,
  MicroRouterOptions,
  MicroRouterStore,
  MicroControl,
} from '../types';
import { buildPathFromSegments, normalizePath, parsePathSegments, safeMarkRaw } from '../utils';
import { useUserStore } from '@stores';
import { usePageTracker } from '@composables';
import { last } from 'lodash';

export function useMicroRouter({ defaultPath }: MicroRouterOptions): MicroRouterStore {
  const storeUser = useUserStore();
  const pageTracker = usePageTracker();

  const { userSettings } = storeToRefs(storeUser);

  const DEFAULT_BM = 'default';
  const DEFAULT_PATH = 'home';

  let sound: Howl | null = null;
  let isVisibilityChange = false;

  const state = reactive({
    activePath: defaultPath,
    fromPath: defaultPath,
    toPath: defaultPath,
    activeDialog: '',
    fromDialog: '',
    toDialog: '',
    routes: new Map<string, MicroRoute>(),
    dialogs: new Map<string, MicroDialog>(),
    controls: new Map<string, MicroControl>(),
  });

  const backgroundMusicVolume = computed(() => {
    if (!userSettings.value || !userSettings.value.background_music) return 0;
    return userSettings.value.background_music / 100;
  });

  function playSound(soundSrc: string, loop = false): Howl {
    cleanSound();
    sound = new Howl({
      src: [`/audios/${soundSrc}.mp3`],
      autoplay: true,
      loop,
      volume: 0,
      html5: true,
    });
    sound.play();
    sound.fade(0, backgroundMusicVolume.value, 2000);
    return sound;
  }

  function stopSound(): void {
    if (sound) sound.stop();
  }

  function pauseSound(): void {
    if (sound) sound.pause();
  }

  function fadeSound(from: number, to: number, duration: number): Promise<void> {
    return new Promise((resolve) => {
      if (sound) {
        sound.fade(from, to, duration);
        sound.once('fade', () => resolve());
      } else resolve();
    });
  }

  function cleanSound(): void {
    if (sound) {
      sound.stop();
      sound.unload();
      sound = null;
    }
  }

  watch(backgroundMusicVolume, (volume) => {
    if (sound) sound.volume(volume);
  });

  function updateBackgroundMusic(route: string): void {
    const routeKey = route.split('/').pop() || DEFAULT_PATH;
    const registeredRoute = state.routes.get(routeKey);
    const soundSrc = registeredRoute?.bgm || DEFAULT_BM;
    cleanSound();
    playSound(soundSrc, true);
  }

  const resolveRoutes = computed<MicroRoute[]>(() => {
    const pathSegments = parsePathSegments(state.activePath);
    return pathSegments
      .map((segment) => state.routes.get(segment))
      .filter((route): route is MicroRoute => route !== undefined);
  });

  const resolveDialogs = computed<MicroDialog[]>(() => {
    return Array.from(state.dialogs.values()).map((dialog) => ({
      ...dialog,
      component: safeMarkRaw(dialog.component),
    }));
  });

  const resolveControls = computed<MicroControl[]>(() => {
    return Array.from(state.controls.values())
      .filter((control) => control.activated)
      .map((control) => ({
        ...control,
        component: safeMarkRaw(control.component),
      }));
  });

  // Navigation logic
  function navigateBack(steps: number): void {
    const segments = parsePathSegments(state.activePath);
    const targetSegments = segments.slice(0, -steps);
    const targetPath = buildPathFromSegments(targetSegments);

    // Track leaving current page
    pageTracker.trackPageLeave(state.activePath, state.activePath, targetPath);

    state.fromPath = state.activePath;
    state.toPath = targetPath;
    state.activePath = targetPath;

    // Track entering new page
    pageTracker.trackPageEnter(targetPath, state.fromPath, targetPath);
  }

  function navigateToPath(path: string, props?: Record<string, unknown>): void {
    const normalizedPath = normalizePath(path);

    // Track leaving current page if different
    if (state.activePath !== normalizedPath) {
      pageTracker.trackPageLeave(state.activePath, state.activePath, normalizedPath);
    }

    state.fromPath = state.activePath;
    state.toPath = normalizedPath;
    state.activePath = normalizedPath;

    if (props) updateRouteProps(normalizedPath, props);

    // Track entering new page
    pageTracker.trackPageEnter(normalizedPath, state.fromPath, normalizedPath);
  }

  function updateRouteProps(path: string, props: Record<string, unknown>): void {
    const segments = parsePathSegments(path);
    const lastSegment = segments[segments.length - 1];
    if (!lastSegment) return;

    const route = state.routes.get(lastSegment);
    if (route) {
      route.attrs = { ...route.attrs, ...props };
    }
  }

  function push(destination: string | number, props?: Record<string, unknown>): void {
    if (!destination) return;

    // Handle backward navigation
    if (typeof destination === 'number' && destination < 0) {
      const steps = Math.abs(destination);
      navigateBack(steps);
      updateBackgroundMusic(state.activePath);
      return;
    }

    const destinationStr = destination.toString();

    // Handle absolute paths
    if (destinationStr.startsWith('/')) {
      navigateToPath(destinationStr, props);
      updateBackgroundMusic(state.activePath);
      return;
    }

    // Handle relative navigation
    const currentSegments = parsePathSegments(state.activePath);

    // Check if destination already exists in current path (navigate back to it)
    const existingIndex = currentSegments.indexOf(destinationStr);
    if (existingIndex !== -1) {
      const targetSegments = currentSegments.slice(0, existingIndex + 1);
      const targetPath = buildPathFromSegments(targetSegments);
      navigateToPath(targetPath, props);
      updateBackgroundMusic(state.activePath);
      return;
    }

    // Append to current path
    const newSegments = [...currentSegments, destinationStr];
    const newPath = buildPathFromSegments(newSegments);
    navigateToPath(newPath, props);
    updateBackgroundMusic(state.activePath);
  }

  // Dialog management
  function createDialogInstance(path: string, attrs?: Record<string, unknown>): DialogInstance {
    return {
      path,
      attrs,
    };
  }

  function executeDialog({ path, open, attrs }: DialogProps): DialogInstance {
    const dialog = state.dialogs.get(path);

    if (!dialog) {
      console.warn(`Dialog with path "${path}" not found`);
      return createDialogInstance(path, attrs);
    }

    void nextTick(() => {
      if (open) {
        // Update dialog state tracking
        state.fromDialog = state.activeDialog;
        state.toDialog = path;
        state.activeDialog = path;

        dialog.actived = true;
        dialog.attrs = {
          path,
          onClose: () => closeDialog(path),
          ...attrs,
        };
      } else {
        // Update dialog state tracking on close
        state.fromDialog = state.activeDialog;
        state.toDialog = '';
        state.activeDialog = '';

        dialog.actived = false;
        dialog.attrs = undefined;
      }
    });

    return createDialogInstance(path, attrs);
  }

  function openDialog(path: string, props?: Record<string, unknown>): DialogInstance {
    const currentContext = state.activeDialog || state.activePath;
    pageTracker.trackDialogEnter(path, currentContext, path);
    return executeDialog({ path, open: true, attrs: props });
  }

  function closeDialog(path: string): void {
    const targetContext = state.activePath;
    pageTracker.trackDialogLeave(path, path, targetContext);
    executeDialog({ path, open: false });
  }

  function toggleControl(name: string, show: boolean): void {
    const control = state.controls.get(name);
    if (!control) {
      console.warn(`Control with name "${name}" not found`);
      return;
    }
    control.activated = show;
    if (show) control.attrs = { name, ...control.attrs };
    else control.attrs = undefined;
  }

  function closeAllDialogs(): void {
    state.dialogs.forEach((dialog, path) => {
      if (dialog.actived) {
        pageTracker.trackDialogLeave(path, path, state.activePath);
      }
      dialog.actived = false;
      dialog.attrs = undefined;
    });

    // Reset dialog state tracking
    state.fromDialog = state.activeDialog;
    state.toDialog = '';
    state.activeDialog = '';
  }

  // Registration methods
  function registerRoute(route: MicroRoute): void {
    const routeWithRawComponent: MicroRoute = {
      ...route,
      component: safeMarkRaw(route.component),
    };
    state.routes.set(route.path, routeWithRawComponent);
  }

  function registerDialog(dialog: MicroDialog): void {
    const dialogWithRawComponent: MicroDialog = {
      ...dialog,
      component: safeMarkRaw(dialog.component),
    };
    state.dialogs.set(dialog.path, dialogWithRawComponent);
  }

  function registerControl(control: MicroControl): void {
    const routeWithRawComponent: MicroControl = {
      ...control,
      component: safeMarkRaw(control.component),
    };
    state.controls.set(control.name, routeWithRawComponent);
  }

  function registerRoutes(routes: MicroRoute[]): void {
    routes.forEach(registerRoute);
  }

  function registerDialogs(dialogs: MicroDialog[]): void {
    dialogs.forEach(registerDialog);
  }

  function registerControls(controls: MicroControl[]): void {
    controls.forEach(registerControl);
  }

  function handleVisibilityChange(): void {
    if (document.hidden) {
      if (sound && !sound.playing()) return;
      if (sound) {
        sound.stop();
        isVisibilityChange = true;
      }
    } else {
      if (sound && isVisibilityChange) {
        sound.play();
        isVisibilityChange = false;
      }
    }
  }

  SeasonalPluginManager.pluginInstances.forEach((plugin) => {
    registerRoutes(plugin.routesGetter || []);
    registerDialogs(plugin.dialogGetter || []);
    registerControls(plugin.GUI || []);
  });

  onMounted(async () => {
    await nextTick();
    // Track initial page enter
    pageTracker.trackPageEnter(state.activePath, undefined, state.activePath);

    setTimeout(() => {
      updateBackgroundMusic(state.activePath);
    }, 500);
    addEventListener('visibilitychange', handleVisibilityChange);
  });

  onBeforeUnmount(() => {
    // Clean up page tracking
    pageTracker.cleanupAllSessions();
    removeEventListener('visibilitychange', handleVisibilityChange);
    cleanSound();
  });

  function normalizedPath(path: string): string {
    const p = last(path.split('/'));
    if (!p) return '';
    return p.replace(/^\//, '');
  }

  const store = {
    _from: computed(() => state.fromPath),
    _to: computed(() => state.toPath),
    activePage: computed(() => normalizedPath(state.activePath)),
    fromPage: computed(() => normalizedPath(state.fromPath)),
    toPage: computed(() => normalizedPath(state.toPath)),
    activeDialog: computed(() => state.activeDialog),
    fromDialog: computed(() => state.fromDialog),
    toDialog: computed(() => state.toDialog),
    resolveRoutes,
    resolveDialogs,
    resolveControls,
    push,
    openDialog,
    closeDialog,
    closeAllDialogs,
    registerRoute,
    registerDialog,
    registerRoutes,
    registerControls,
    registerDialogs,
    toggleControl,
    playSound,
    stopSound,
    pauseSound,
    fadeSound,
  };

  // Provide store for child components
  provide(MICRO_ROUTER.STORE, store);

  return store;
}

export function useMicroRoute(): MicroRouterStore {
  const store = inject<MicroRouterStore>(MICRO_ROUTER.STORE);

  if (!store) {
    throw new Error(
      'useMicroRoute must be called within a component that has access to the MicroRouter store',
    );
  }

  return store;
}
