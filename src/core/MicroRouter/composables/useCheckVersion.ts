export function useCheckVersion(body: {
  buildVersion: string;
  settings: Ref<{
    frontend_build: {
      version: string;
      last_update: string;
    };
  }>;

  fetchSetting: () => void;
  callback: () => void;
  socket: any;
}) {
  const checkVersion = () => {
    if (
      !body.settings.value?.frontend_build?.version ||
      !body.buildVersion ||
      body.settings.value.frontend_build.version === body.buildVersion
    )
      return;
    body.callback();
  };

  onMounted(() => {
    checkVersion();
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        body.fetchSetting();
        checkVersion();
      }
    });
    body.socket.on('update_build', () => {
      body.callback();
    });
  });
}
