<template>
  <transition-group name="page-slide" :css="cssTransition" @enter="onEnter">
    <RoutePage
      v-for="(route, i) in resolveRoutes"
      :style="{
        transition: cssTransition ? 'transform .5s cubic-bezier(0.65, 0, 0.35, 1)' : 'none',
        'z-index': i,
      }"
      :class="{
        deactive: resolveRoutes.length > 1 && i !== resolveRoutes.length - 1,
      }"
      :key="route.path"
    >
      <component :is="route.component" v-bind="route.attrs" />
    </RoutePage>
  </transition-group>
  <q-dialog
    :key="`dialog-${dialog.path}`"
    v-for="dialog in resolveDialogs"
    maximized
    :persistent="dialog.persistent"
    :position="dialog.position"
    :transitionShow="dialog.transitionShow"
    :transitionHide="dialog.transitionHide"
    v-model="dialog.actived"
  >
    <component :is="dialog.component" v-bind="dialog.attrs" />
  </q-dialog>
  <div class="GUI">
    <transition-group name="list">
      <component
        v-for="control in resolveControls"
        :key="control.name"
        :is="control.component"
        v-bind="control.attrs"
      />
    </transition-group>
  </div>
</template>

<script setup lang="ts">
import gsap, { Power2 } from 'gsap';
import { delay, intersection } from 'lodash';

gsap.config({
  force3D: true,
  nullTargetWarn: false,
});

const { _from, _to, resolveRoutes, resolveDialogs, resolveControls } = useMicroRouter({
  defaultPath: 'home',
});

const cssTransition = computed(() => {
  const to = _from.value.split('/').filter(Boolean);
  const from = _to.value.split('/').filter(Boolean);
  return Boolean(intersection(to, from).length);
});

function onEnter(el: Element, done: () => void): void {
  if (!cssTransition.value)
    gsap.fromTo(
      el,
      {
        xPercent: -100,
      },
      {
        xPercent: 0,
        duration: 0.5,
        ease: Power2.easeInOut,
        onComplete: done,
      },
    );
  else delay(done, 500);
}
</script>
