@use 'src/css/mixin.scss';

.sqk-page {
  margin: auto;
  position: absolute !important;
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s cubic-bezier(0.65, 0, 0.35, 1);
    content: '';
    z-index: 0;
    pointer-events: none;
  }

  &.deactive {
    transform: translateX(-20%);
  }

  transform: translateX(0);

  @include mixin.fit;
  @include mixin.fullscreen;

  &__body {
    @include mixin.fit;
  }
}

.sqk-page.deactive::before {
  opacity: 1;
}

.page-slide-enter-from,
.page-slide-leave-to {
  transform: translateX(100%);
}

.GUI {
  position: fixed;
  width: 0;
  top: 0;
  left: 0;
  right: 0;
  overflow: visible;
  z-index: 1000;
}

.list-move, /* apply transition to moving elements */
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* ensure leaving items are taken out of layout flow so that moving
   animations can be calculated correctly. */
.list-leave-active {
  position: absolute;
}
