import { uniq } from 'lodash';

export function normalizePath(path: string): string {
  return path.startsWith('/') ? path : `/${path}`;
}

export function parsePathSegments(path: string): string[] {
  return uniq(path.split('/').filter(Boolean));
}

export function buildPathFromSegments(segments: string[]): string {
  return `/${segments.join('/')}`;
}

export function safeMarkRaw<T extends object>(component: T): T {
  try {
    return markRaw(component) as T;
  } catch (error) {
    console.warn('Failed to mark component as raw:', error);
    return component;
  }
}
