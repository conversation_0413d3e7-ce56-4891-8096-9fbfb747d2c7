import { MicroControl, MicroDialog, MicroRoute } from '@core/MicroRouter';

export abstract class GamePlugin {
  constructor(
    public name: string,
    public routesGetter?: MicroRoute[],
    public dialogGetter?: MicroDialog[],
    public GUI?: MicroControl[],
  ) {}

  abstract getPublicAPI<T>(): T;
}
interface GamePluginManifest {
  name: string;
  plugins: GamePlugin[];
}

export class SeasonalPluginManager {
  static pluginManifests: GamePlugin[] = [];
  static pluginInstances = new Map<any, GamePlugin>();
  static registerPlugin<T extends GamePlugin>(plugin: T) {
    this.pluginManifests.push(plugin);
    this.pluginInstances.set(plugin.name, plugin);
  }

  static getPluginAPI<T extends GamePlugin>(name: string): T | undefined {
    return this.pluginInstances.get(name) as T | undefined;
  }

  static getEnabledPlugins(): ComputedRef<GamePlugin[]> {
    return computed(() => this.pluginManifests);
  }

  static isPluginEnabled<T extends GamePlugin>(pluginClass: new (...args: any[]) => T): boolean {
    return this.pluginInstances.has(pluginClass);
  }

  static initializePlugins(manifest: GamePluginManifest): GamePlugin[] {
    this.pluginManifests = manifest.plugins;
    this.pluginManifests.forEach((plugin) => {
      this.pluginInstances.set(plugin.constructor, plugin);
    });
    return this.pluginManifests;
  }
}
