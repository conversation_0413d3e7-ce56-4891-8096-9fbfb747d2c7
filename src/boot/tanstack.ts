import { boot } from 'quasar/wrappers';
import { VueQueryPlugin } from '@tanstack/vue-query';

export default boot(({ app }) => {
  app.use(VueQueryPlugin, {
    queryClientConfig: {
      defaultOptions: {
        queries: {
          retry: false,
          retryOnMount: true,
          refetchOnWindowFocus: true,
          refetchOnReconnect: true,
          experimental_prefetchInRender: true,
        },
      },
    },
  });
});
