import { boot } from 'quasar/wrappers';
import { createI18n } from 'vue-i18n';
import { TUserLang } from '@types';
import { UserModel } from '@services';

type Translations = Record<string, Record<string, string>>;

const lang = computed((): TUserLang => {
  const l = LocalStorage.getItem<TUserLang>('lang');
  if (l) return l;
  const env = process.env.APP_LANGUAGE_CODE;
  if (env) return env as TUserLang;
  return 'en' as TUserLang;
});

export default boot(async ({ app }) => {
  const { data } = await UserModel.localize();

  const messages = data.reduce<Translations>((acc, { lang: locale, data: translation }) => {
    if (!acc[locale]) {
      acc[locale] = Object.fromEntries(
        Object.entries(translation).map(([key, value]) => [
          key,
          value === '' || value === null || value === undefined ? key : value,
        ]),
      );
    }
    return acc;
  }, {});

  const i18n = createI18n({
    messages,
    legacy: false,
    warnHtmlMessage: false,
    warnHtmlInMessage: 'off',
    locale: lang.value,
    fallbackLocale: 'en',
    missingWarn: false,
  });

  app.use(i18n);
});
