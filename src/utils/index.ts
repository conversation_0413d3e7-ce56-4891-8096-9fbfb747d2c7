export * from './constants';
export * from './country';

import dayjs, { Dayjs } from 'dayjs';
import numeral from 'numeral';
import { Notify } from 'quasar';
import { EMAIL_REGEX, FULL_DATE_TIME_FORMAT, SINGAPORE_PHONE_REGEX } from './constants';
import { NotifyOtp } from './notify';
import { useUserStore } from '@stores';
import { CountryIsoCode } from '@types';
import { CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';
import { last } from 'lodash';
import { twMerge } from 'tailwind-merge';
import clsx, { ClassValue } from 'clsx';
import duration from 'dayjs/plugin/duration';
import utc from 'dayjs/plugin/utc';
import isBetween from 'dayjs/plugin/isBetween';

dayjs.extend(duration);
dayjs.extend(utc);
dayjs.extend(isBetween);

let dismiss: (opt?: NotifyOtp) => void;

export function showNotify(options: NotifyOtp) {
  dismiss != void 0 && dismiss();
  if (!options) return;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  dismiss = Notify.create({ ...options, html: true });
  return dismiss;
}

export function closeNotify() {
  dismiss && dismiss();
}

export function successNotify(options: NotifyOtp) {
  return showNotify({
    ...options,
    classes: options.classes || 'notify-success',
    position: options.position || 'top',
    timeout: options.timeout || 5000,
    actions: options.actions || [
      {
        handler: closeNotify,
      },
    ],
  });
}

export function errorNotify(options: NotifyOtp) {
  return showNotify({
    ...options,
    classes: options.classes || 'notify-error',
    position: options.position || 'top',
    timeout: options.timeout || 5000,
    actions: options.actions || [
      {
        handler: closeNotify,
      },
    ],
  });
}

export function numeralFormat(number: number, type = 'AUTO'): string {
  if (type === 'AUTO') {
    switch (true) {
      case Number.isNaN(number):
        type = '0,0';
        break;
      case Math.floor(number) !== number:
        type = '0,0.00';
        break;
      default:
        type = '0,0';
        break;
    }
  }
  return numeral(number).format(type);
}

export function removeSpecialKey(str: string, specialKey = '_'): string {
  if (str) {
    const convertToArray = str.toLowerCase().split(specialKey);
    const result = convertToArray.map(function (val) {
      return val.replace(val.charAt(0), val.charAt(0).toUpperCase());
    });
    return result.join(' ');
  }
  return '';
}

export function removeSpace(str: string): string {
  return str.replace(/\s/g, '');
}

export function dateTimeFormat(
  date: Dayjs | string | number | Date,
  format = FULL_DATE_TIME_FORMAT,
): string {
  return dayjs(date).format(format);
}

export function isDifferentDay(date1: string, date2: string): boolean {
  if (!date1 || !date2) return false;
  const d1 = dayjs(date1, 'YYYY-MM-DD');
  const d2 = dayjs(date2, 'YYYY-MM-DD');
  return !!dayjs(d1).diff(d2, 'day');
}

export function isSameDay(date1: string, date2: string): boolean {
  if (!date1 || !date2) return false;
  const d1 = dayjs(date1, 'YYYY-MM-DD');
  const d2 = dayjs(date2, 'YYYY-MM-DD');
  return !!dayjs(d1).isSame(d2, 'day');
}

export function convertTime(milisecons: number) {
  const mils = Math.max(milisecons, 0);
  const days = Math.floor(mils / (1000 * 60 * 60 * 24));
  const hours = Math.floor((mils / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((mils / 1000 / 60) % 60);
  const seconds = Math.floor((mils / 1000) % 60);

  return {
    days: ('0' + days).slice(-2),
    hours: ('0' + hours).slice(-2),
    seconds: ('0' + seconds).slice(-2),
    minutes: ('0' + minutes).slice(-2),
  };
}

export function timeCountDown(time: number): string {
  if (time <= 0) return `00s`;

  const { days, hours, minutes, seconds } = convertTime(time);

  if (+days > 0) {
    return `${days}$d ${hours}$h`;
  } else if (+hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (+minutes > 0) {
    return `${minutes}m ${seconds}s`;
  } else {
    return `${seconds}s`;
  }
}

export function isSingaporeNumber(mobile_number: string): boolean {
  return SINGAPORE_PHONE_REGEX.test(mobile_number);
}

export function isEmailValid(email: string): boolean {
  return EMAIL_REGEX.test(email);
}

export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

export function playSFX(src: string, loop = false): Howl {
  const storeUser = useUserStore();
  const { userSettings } = storeToRefs(storeUser);

  const soundEffectVolume = computed(() => {
    if (!userSettings.value || !userSettings.value.sound_effect) return 0;
    return userSettings.value.sound_effect / 100;
  });

  const sfx = new Howl({
    src: [`/audios/${src}.mp3`],
    volume: soundEffectVolume.value,
    html5: true,
    loop,
    onend: () => {
      if (!loop) {
        sfx.stop();
        sfx.unload();
      }
    },
  });
  sfx.play();
  return sfx;
}

export function delay(ms: number): Promise<unknown> {
  let t: NodeJS.Timeout;
  return new Promise((resolve) => {
    t = setTimeout(resolve, ms);
  }).finally(() => clearTimeout(t));
}

export const MOBILE_NUMBER_PATTERNS: Record<CountryIsoCode, RegExp> = {
  SG: /^\+?65(8|9)\d{7}$/,
  MY: /^(\+?6?01)[0|1|2|3|4|6|7|8|9]\-*[0-9]{7,8}$/,
  ID: /^\+?62(8)\d{7,10}$/,
  TH: /^\+?66(08|09)\d{8}$/,
  VN: /^\+?84(3|5|7|8|9)\d{8}$/,
  JP: /^(\+?81[ \-]?(\(0\))?|0)[6789]0[ \-]?\d{4}[ \-]?\d{4}$/,
} as const;

export function isValidMobileNumber(mobileNumber: string, country?: CountryIsoCode): boolean {
  if (country) {
    const pattern = MOBILE_NUMBER_PATTERNS[country];
    return pattern?.test(mobileNumber) ?? false;
  }

  return Object.values(MOBILE_NUMBER_PATTERNS).some((pattern) => pattern.test(mobileNumber));
}
export function beautifyPhoneNumber(mobileNumber: string, countryCode?: CountryCode): string {
  if (!mobileNumber?.trim()) return mobileNumber;

  const numberToParse = countryCode
    ? mobileNumber
    : mobileNumber.startsWith('+')
      ? mobileNumber
      : `+${mobileNumber}`;

  const phoneNumber = parsePhoneNumberFromString(numberToParse, countryCode);

  return phoneNumber?.isValid() ? phoneNumber.formatInternational() : mobileNumber;
}

export function metaPixelTacking(type: string) {
  if (typeof (window as any).fbq === 'function') {
    (window as any).fbq('track', type);
  } else {
    console.error('fbq is not a function. Please check the Facebook Pixel initialization.');
  }
}

export function extractPromoCode(input: string): string {
  if (!input.trim()) return '';

  const extractFromQuery = (text: string, param: string): string => {
    const parts = text.split(`?=${param}`);
    return parts.length > 1 ? last(parts) || '' : '';
  };

  const codeFromLongParam = extractFromQuery(input, 'code');
  if (codeFromLongParam) return codeFromLongParam;

  const codeFromShortParam = extractFromQuery(input, 'c');
  if (codeFromShortParam) return codeFromShortParam;

  return input.trim();
}

export function getSocials() {
  return [
    {
      link: 'https://t.me/sqkiisg',
      icon: 'telegram',
    },
    {
      link: 'https://www.facebook.com/sqkii',
      icon: 'fb',
    },
    {
      link: 'https://www.instagram.com/sqkiimouse',
      icon: 'insta',
    },
    {
      link: 'https://www.tiktok.com/@sqkiimouse',
      icon: 'tiktok',
    },
  ];
}
