<script lang="ts" setup>
import { Application } from 'pixi.js';
import { usePixiAnimations } from '@composables';

interface Props {
  name: string;
  json: string;
  animationSpeed?: number;
  size?: 'cover' | 'contain';
  width?: number;
  height?: number;
}

interface PixiInstance {
  app: Application;
  destroy: () => void;
  clearCache: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  animationSpeed: 0.5,
  size: 'cover',
});

const pixiCanvas = ref<HTMLCanvasElement>();
const pixiInstance = ref<PixiInstance>();

async function initializePixi(): Promise<void> {
  if (!pixiCanvas.value) {
    throw new Error('Canvas element not found');
  }

  try {
    const instance = await usePixiAnimations({
      id: pixiCanvas.value,
      name: props.name,
      json: props.json,
      animationSpeed: props.animationSpeed,
      size: props.size,
      width: props.width || 0,
      height: props.height || 0,
    });

    pixiInstance.value = instance;
  } catch (err) {
    console.error('PIXI Animation Error:', err);
  }
}

function cleanup(): void {
  if (pixiInstance.value) {
    pixiInstance.value.destroy();
    pixiInstance.value.clearCache();
    pixiInstance.value = undefined;
  }
}

onMounted(async () => {
  await nextTick();
  await initializePixi();
});

onBeforeUnmount(() => {
  cleanup();
});
</script>

<template>
  <canvas ref="pixiCanvas" />
</template>
