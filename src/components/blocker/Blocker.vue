<script setup lang="ts">
import QrcodeVue from 'qrcode.vue';

const { t } = useI18n();

const URL = process.env.APP_END_POINT as string;
const userAgent = navigator.userAgent.toLowerCase();
const tabletRegex =
  /(ipad|tablet|(android(?!.*mobile))|(windows(?!.*phone)(.*touch))|kindle|playbook|silk|(puffin(?!.*(IP|AP|WP))))/;
const isTablet = computed(() => tabletRegex.test(userAgent));
</script>
<template>
  <div class="text-center fullscreen blocker">
    <div class="flex w-full logo flex-center">
      <Icon
        name="logo_htm"
        class="w-auto mx-auto max-h-[132px] blocker__logo"
        style="height: 15vh"
      />
    </div>
    <img src="/imgs/blocker/new_car_silver.png" class="object-cover kv" />
    <div class="items-center gap-5 mx-auto bottom-8 right-8 flex-nowrap blocker_info">
      <div class="pb-5">
        <div
          class="text-2xl font-bold whitespace-nowrap"
          :class="{ 'text-left': !isTablet }"
          v-html="t('BLOCKER_CTA')"
        ></div>
        <div class="text-2xl font-400 whitespace-nowrap" v-html="t('BLOCKER_CTA_DESC')"></div>
      </div>
      <div class="p-2 bg-white rounded-md">
        <QrcodeVue :value="URL" :size="155" level="H" />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.blocker {
  background: url('/imgs/blocker/new_kv_tablet.png') no-repeat;
  background-size: cover;
  .kv {
    position: absolute;
    bottom: 2vh;
    left: 50%;
    transform: translateX(-50%);
    height: 45vh;
  }
  .blocker__logo {
    margin-top: 60px;
  }
  .blocker_info {
    display: flex;
    flex-direction: column-reverse;
    margin-top: 30px;
    img {
      margin-bottom: 20px;
      height: 120px !important;
    }
  }
  @media screen and (min-width: 1025px) {
    background: url('/imgs/blocker/new_kv.png') no-repeat !important;
    background-size: cover !important;
    .kv {
      position: absolute;
      bottom: 12%;
      left: 50%;
      transform: translateX(-50%);
      height: 60vh !important;
      width: auto !important;
    }
    .blocker__logo {
      margin-top: 40px;
    }
    .blocker_info {
      position: absolute;
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      margin-top: 30px;
      img {
        margin-bottom: 20px;
        height: 120px !important;
      }
    }
  }
}
</style>
