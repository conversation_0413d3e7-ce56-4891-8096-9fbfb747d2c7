import { SVUser } from '@types';
import { STORAGE_KEYS } from '@enums';

interface SVState {
  token: string | null;
  svUser: SVUser | null;
}

export const useSVStore = defineStore('vouchers', {
  state: (): SVState => ({
    token: LocalStorage.getItem(STORAGE_KEYS.SV_TOKEN) || null,
    svUser: null,
  }),

  getters: {
    isSVAuthenticated: (state) => !!state.token,
  },

  actions: {
    setUser(user: SVUser): void {
      this.svUser = user;
    },

    setUserBalance(balance: number): void {
      this.svUser = { ...this.svUser, balance } as SVUser;
    },

    setToken(token: string): void {
      this.token = token;
      LocalStorage.set(STORAGE_KEYS.SV_TOKEN, token);
    },

    updateUser(fieldsToUpdate: Partial<SVUser>): void {
      this.svUser = { ...this.svUser, ...fieldsToUpdate } as SVUser;
    },
  },
});
