import { STORAGE_KEYS } from '@enums';
import {
  MerchantAcquisition,
  MerchantAcquisitionType,
  Notification,
  Season,
  Settings,
  TimelineLogs,
  TUserLang,
  User,
} from '@types';
import dayjs from 'dayjs';

interface UserState {
  token: string | null;
  user: User | null;
  currentCountryCode: string;
  notifications: Notification[];
  timelineLogs: TimelineLogs[];
  gameSettings: Settings | null;
  currentSeason: Season | undefined;
  merchantAcquisition: MerchantAcquisition;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: LocalStorage.getItem(STORAGE_KEYS.HTM_TOKEN) || null,
    user: null,
    currentCountryCode: '',
    notifications: [],
    timelineLogs: [],
    gameSettings: null,
    currentSeason: undefined,
    merchantAcquisition: {
      visited: Number(LocalStorage.getItem(STORAGE_KEYS.SV_VISITED) || 0),
      closed_callout: Number(LocalStorage.getItem(STORAGE_KEYS.SV_CLOSED_CALLOUT) || 0),
      max_submit: 0,
      total_submit: 0,
      show_callout: false,
      last_type:
        (LocalStorage.getItem(STORAGE_KEYS.SV_LAST_TYPE) as MerchantAcquisitionType) || 'owners',
      submitted: false,
    },
  }),
  getters: {
    isAuthenticated: (state) => !!state.token,
    userSettings: (state) => state.user?.setting || null,
    crystals: (state) => state.user?.resources.crystal || 0,
    initialSlide: (state) => {
      if (!state.timelineLogs.length) return 0;
      const index = state.timelineLogs.findIndex((t) => t.status === 'ongoing');
      return index >= 0 ? index : 0;
    },
    gameFeatures: (state) => state.gameSettings?.features,
    gameFlags: (state) => state.gameSettings?.flags,
    seasonCode: () => process.env.APP_COUNTRY_CODE || 'SG',
    campaignName: () => process.env.APP_CAMPAIGN_NAME || 'SG',
    deploymentNumber: () => process.env.APP_DEPLOYMENT_NUMBER || '',
    appLanguageCode: () => process.env.APP_LANGUAGE_CODE as TUserLang,
    isTestingEnv: () => Boolean(process.env.APP_TESTING_ENV),
    turnstileDumyToken: () => process.env.APP_TURNSTILE_DUMMY_TOKEN as string,
    isUserLogged: (state) => Boolean(state.user?.mobile_number),
    triggerEndGameSurvey: (state) =>
      state.gameSettings?.flags.end_game_survey && !state.user?.end_survey,
    triggerMidGameSurvey: (state) => {
      if (!state.user || !state.currentSeason) return false;
      if (state.user.survey) return false;
      if (+new Date(state.currentSeason.start_at) > +new Date()) return false;

      const today = dayjs();
      const startHunt = dayjs(state.currentSeason.start_at);
      const joinedHunt = dayjs(state.user.created_at);
      const daysSinceJoin = today.diff(joinedHunt, 'day');
      const daysSinceHunt = today.diff(startHunt, 'day');

      if (daysSinceHunt < 2) return false;
      return daysSinceJoin >= 3;
    },
  },
  actions: {
    setUser(user: User): void {
      this.user = user;
    },

    setToken(token: string): void {
      this.token = token;
      LocalStorage.set(STORAGE_KEYS.HTM_TOKEN, token);
    },

    updateUser(fieldsToUpdate: Partial<User>): void {
      this.user = { ...this.user, ...fieldsToUpdate } as User;
    },

    setNotifications(notifications: Notification[]): void {
      const n = notifications.filter((n) => !n.should_show);
      this.notifications = n;
    },

    setTimelineLogs(timelineLogs: TimelineLogs[]): void {
      this.timelineLogs = timelineLogs;
    },

    setSettings(settings: Settings): void {
      const now = new Date().getTime();
      const season = settings.seasons.find(
        (s) => new Date(s.start_at).getTime() <= now && now < new Date(s.end_at).getTime(),
      );
      this.gameSettings = settings;
      this.currentSeason = season;
    },

    setMerchantAcquisition(fieldsToUpdate: Partial<MerchantAcquisition>): void {
      this.merchantAcquisition = {
        ...this.merchantAcquisition,
        ...fieldsToUpdate,
      };
    },

    checkShowCallout(): void {
      const { total_submit, max_submit, closed_callout, visited } = this.merchantAcquisition;

      // Don't show if max submissions reached or callout closed too many times
      if (total_submit >= max_submit || closed_callout >= 3) {
        this.merchantAcquisition.show_callout = false;
        return;
      }

      // Show callout based on visit frequency (powers of 2 starting from 3)
      const shouldShow = visited >= 3 && Number.isInteger(Math.log2(visited / 3));
      this.merchantAcquisition.show_callout = shouldShow;

      // Toggle between types when showing callout for subsequent visits
      if (shouldShow && visited > 3) {
        this.merchantAcquisition.last_type =
          this.merchantAcquisition.last_type === 'owners' ? 'friends' : 'owners';
        LocalStorage.set(STORAGE_KEYS.SV_LAST_TYPE, this.merchantAcquisition.last_type);
      }
    },

    updateMerchantAcquisition(type: 'visited' | 'closed_callout'): void {
      const storageKey =
        type === 'visited' ? STORAGE_KEYS.SV_VISITED : STORAGE_KEYS.SV_CLOSED_CALLOUT;
      const currentValue = Number(LocalStorage.getItem(storageKey)) || 0;
      const newValue = currentValue + 1;
      this.merchantAcquisition[type] = newValue;
      LocalStorage.set(storageKey, newValue);
      this.checkShowCallout();
    },
  },
});
