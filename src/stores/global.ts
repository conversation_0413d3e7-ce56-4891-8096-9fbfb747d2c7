import { DevTools, SocketStore } from '@types';
import { Socket } from 'socket.io-client';

interface GlobalState {
  ioStore: SocketStore;
  defaultPath: string;
  lastUserLocations: [number, number];
  devTools: DevTools;
}

export const useGlobalStore = defineStore('global', {
  state: (): GlobalState => ({
    ioStore: {},
    defaultPath: 'home',
    lastUserLocations: [0, 0], // [lng,lat]
    devTools: {
      fakeGps: false,
      location: [0, 0],
      realLocation: [0, 0],
      isEnableGPS: false,
      pickLocationMode: false,
      targetLocation: [0, 0],
    },
  }),
  getters: {
    //
  },
  actions: {
    setSocket(io: { socket?: Socket; authenticated?: boolean }): void {
      this.ioStore = { ...this.ioStore, ...io };
    },

    setUserLocation([lng, lat]: [number, number]): void {
      if (process.env.APP_TESTING_ENV && this.devTools.fakeGps) {
        this.devTools.realLocation = [lng, lat];
        return;
      }
      this.lastUserLocations = [lng, lat];
    },
  },
});
