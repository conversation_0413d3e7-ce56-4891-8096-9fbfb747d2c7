export enum BrandSponsors {
  Sqkii = 'sqkii',
  DBS = 'dbs',
}

export const BrandSponsorsLabels = {
  [BrandSponsors.Sqkii]: 'Sqkii',
  [BrandSponsors.DBS]: 'DBS',
};

export enum SqkiiVouchersTransactionType {
  TOP_UP = 'topup',
  PAYMENT = 'payment',
}

export const SqkiiVouchersTransactionTypeLabels = {
  [SqkiiVouchersTransactionType.TOP_UP]: 'Get Vouchers',
  [SqkiiVouchersTransactionType.PAYMENT]: 'Use Vouchers',
};

export enum SqkiiVouchersTransactionStatus {
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

export const STORAGE_KEYS = {
  HTM_TOKEN: `${process.env.APP_NAME}_HTM_TOKEN`,
  LANGUAGE: 'LANG',
  SV_TOKEN: `${process.env.APP_NAME}_SV_TOKEN`,
  SV_VISITED: `${process.env.APP_NAME}_SV_VISITED`,
  SV_CLOSED_CALLOUT: `${process.env.APP_NAME}_SV_CLOSED_CALLOUT`,
  SV_LAST_TYPE: `${process.env.APP_NAME}_SV_LAST_TYPE`,
  CAMERA_PERMISSION: 'CAMERA_PERMISSION',
} as const;
